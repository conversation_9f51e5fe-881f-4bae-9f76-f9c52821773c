<?php

namespace app\applent\validate\recharge;

use app\common\validate\BaseValidate;

/**
 * 充值验证器
 * Class RechargeValidate
 * @package app\applent\validate\recharge
 */
class RechargeValidate extends BaseValidate
{
    protected $rule = [
        // 创建订单
        'package_id' => 'require|integer',
        //支付方式
        'pay_method_id' => 'require',
        // 调起支付
        'order_id' => 'require|integer',
        // 查询支付状态
        'order_no' => 'require|length:6,32',
    ];

    protected $message = [
        // 创建订单错误信息
        'package_id.require' => '请选择充值套餐',
        'package_id.integer' => '套餐ID格式错误',
        //支付方式错误信息
        'pay_method_id.require' => '请选择支付方式',
        // 调起支付错误信息
        'order_id.require' => '订单ID不能为空',
        'order_id.integer' => '订单ID格式错误',
        // 查询支付状态错误信息
        'order_no.require' => '订单号不能为空',
        'order_no.length' => '订单号长度必须在6-32位之间',
    ];

    /**
     * @notes 创建订单场景
     * @return RechargeValidate
     */
    public function sceneCreateOrder()
    {
        return $this->only(['package_id','pay_method_id']);
    }

    /**
     * @notes 调起支付场景
     * @return RechargeValidate
     */
    public function sceneInitiatePayment()
    {
        return $this->only(['order_id']);
    }

    /**
     * @notes 查询支付状态场景
     * @return RechargeValidate
     */
    public function sceneQueryPayStatus()
    {
        return $this->only(['order_no']);
    }

    /**
     * @notes 查询订单状态场景（前端轮询）
     * @return RechargeValidate
     */
    public function sceneQueryOrderStatus()
    {
        return $this->only(['order_id']);
    }
}
