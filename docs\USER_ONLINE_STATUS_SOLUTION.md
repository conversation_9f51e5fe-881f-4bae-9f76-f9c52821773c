# 用户在线状态完整解决方案

## 🎯 问题描述

在原有系统中，用户在线状态主要依赖缓存 `Cache::set("user_online_{$userId}", time(), 3600)`，但存在以下问题：

1. **缓存过期问题**：缓存过期后，即使用户实际还在线，系统也会认为用户离线
2. **数据不一致**：缓存、数据库、IM服务器的在线状态可能不一致
3. **单点故障**：完全依赖缓存，缓存失效时无回退机制

## 🔧 解决方案

### 1. 多层检查机制

创建了 `UserOnlineService` 服务类，实现多层在线状态检查：

```php
// 使用方法
use app\common\service\UserOnlineService;

// 检查单个用户是否在线
$isOnline = UserOnlineService::isUserOnline($userId);

// 批量检查用户在线状态
$onlineUsers = UserOnlineService::batchCheckOnlineStatus([1001, 1002, 1003]);
```

### 2. 检查流程

#### 第一层：缓存检查
```php
$cacheOnline = Cache::get("user_online_{$userId}");
if ($cacheOnline) {
    return true; // 缓存命中，直接返回在线
}
```

#### 第二层：数据库检查 + 智能缓存重建
```php
$user = User::where('user_id', $userId)->field('is_online,last_online_time')->find();

if ($user['is_online'] == 1) {
    $lastOnlineTime = $user['last_online_time'] ?? 0;
    $currentTime = time();
    
    // 如果最后在线时间在2小时内，认为用户可能还在线
    if (($currentTime - $lastOnlineTime) <= 7200) {
        // 重新设置缓存，但缩短过期时间为30分钟
        Cache::set("user_online_{$userId}", $currentTime, 1800);
        return true;
    }
}
```

#### 第三层：IM服务器查询（终极验证）
```php
// 通过腾讯云IM查询真实在线状态
$result = im_check_user_online_state($userId);
if ($result['QueryResult'][0]['State'] === 'Online') {
    // 同步状态到本地
    self::syncOnlineStatus($userId, true);
    return true;
}
```

### 3. 核心功能

#### 3.1 智能状态同步
```php
// 自动同步IM服务器状态到本地数据库和缓存
UserOnlineService::syncOnlineStatus($userId, $isOnline);
```

#### 3.2 批量状态检查
```php
// 高效批量检查，适用于用户列表、动态列表等场景
$userIds = [1001, 1002, 1003, 1004];
$onlineUsers = UserOnlineService::batchCheckOnlineStatus($userIds);
// 返回: [1001, 1003] (在线用户ID数组)
```

#### 3.3 缓存刷新机制
```php
// 主动刷新用户在线缓存
UserOnlineService::refreshUserOnlineCache($userId, 3600);
```

#### 3.4 过期状态清理
```php
// 定时任务：清理长时间未活跃的在线状态
$cleanedCount = UserOnlineService::cleanExpiredOnlineStatus();
```

## 🚀 使用场景

### 1. 用户列表显示
```php
// 在用户列表中显示在线状态
foreach ($users as &$user) {
    $user['is_online_real'] = UserOnlineService::isUserOnline($user['user_id']);
}
```

### 2. 视频通话发起
```php
// 发起视频通话前检查对方是否在线
if (!UserOnlineService::isUserOnline($targetUserId)) {
    return ['code' => 0, 'msg' => '对方当前不在线'];
}
```

### 3. 消息推送优化
```php
// 只向真正在线的用户推送消息
$onlineUsers = UserOnlineService::batchCheckOnlineStatus($userIds);
foreach ($onlineUsers as $userId) {
    // 发送推送消息
    sendPushNotification($userId, $message);
}
```

### 4. 动态列表排序
```php
// 动态列表优先显示在线用户
$dynamics = DynamicModel::select();
foreach ($dynamics as &$dynamic) {
    $dynamic['user_online'] = UserOnlineService::isUserOnline($dynamic['user_id']);
}

// 按在线状态排序
usort($dynamics, function($a, $b) {
    return $b['user_online'] - $a['user_online'];
});
```

## 🔄 集成到现有系统

### 1. 更新IM回调逻辑
已更新 `ImCallbackLogic.php`：
```php
// 用户上线
UserOnlineService::setUserOnlineStatus($userId, true, 3600);

// 用户下线  
UserOnlineService::setUserOnlineStatus($userId, false);
```

### 2. 替换现有在线检查
将现有的简单缓存检查：
```php
// 旧方式
$isOnline = Cache::get("user_online_{$userId}") ? true : false;
```

替换为：
```php
// 新方式
$isOnline = UserOnlineService::isUserOnline($userId);
```

### 3. 添加定时清理任务
在定时任务中添加：
```php
// 每小时执行一次，清理过期的在线状态
UserOnlineService::cleanExpiredOnlineStatus();
```

## 📊 性能优化

### 1. 缓存策略
- **正常情况**：缓存1小时 (3600秒)
- **重建缓存**：缓存30分钟 (1800秒)
- **批量查询**：使用批量接口减少数据库查询

### 2. 降级策略
- **IM服务异常**：回退到数据库查询
- **数据库异常**：返回false，避免系统崩溃
- **缓存异常**：直接查询数据库

### 3. 日志记录
所有关键操作都有详细日志记录，便于问题排查：
```php
LogService::write('用户在线状态缓存重建', [
    'user_id' => $userId,
    'last_online_time' => $lastOnlineTime,
    'cache_duration' => 1800
], 'user_online');
```

## ✅ 优势总结

### 1. **可靠性提升**
- 多层检查机制，避免单点故障
- 智能缓存重建，减少误判
- IM服务器终极验证，确保准确性

### 2. **性能优化**
- 缓存优先，减少数据库查询
- 批量检查，提高效率
- 智能过期时间，平衡性能和准确性

### 3. **易于维护**
- 统一的服务接口
- 详细的日志记录
- 完善的异常处理

### 4. **向后兼容**
- 不破坏现有接口
- 渐进式升级
- 保持原有功能

## 🎯 最佳实践

1. **优先使用服务类**：统一通过 `UserOnlineService` 检查在线状态
2. **合理设置缓存时间**：根据业务需求调整缓存过期时间
3. **定期清理过期状态**：避免数据库中积累过多无效的在线状态
4. **监控日志**：关注在线状态相关的日志，及时发现问题
5. **批量操作优化**：对于大量用户的在线状态检查，使用批量接口

这个解决方案彻底解决了缓存过期导致的在线状态误判问题，提供了更可靠、更高效的在线状态管理机制！
