<?php

namespace app\applent\controller;

use app\applent\logic\ImCallbackLogic;
use app\common\service\LogService;

/**
 * IM回调控制器
 * 处理腾讯云IM的回调事件
 * 当前支持：用户状态变更回调、单聊消息回调
 * Class ImCallbackController
 * @package app\applent\controller
 */
class ImCallbackController extends BaseApiController
{
    /**
     * 不需要登录验证的方法
     * @var array
     */
    public array $notNeedLogin = ['callback'];

    /**
     * IM回调接口
     * 接收并处理腾讯云IM的回调事件
     * 当前支持：用户状态变更回调、单聊消息回调
     * 注意：已移除签名验证，直接处理回调数据
     * @return void
     */
    public function callback()
    {
        try {
            // 接收回调数据
            $json = file_get_contents('php://input');
            $callbackData = json_decode($json, true);

            // 记录回调数据接收日志
            LogService::write('IM回调数据接收', [
                'command' => $callbackData['CallbackCommand'] ?? 'unknown',
                'user_id' => $this->extractUserId($callbackData),
                'event_time' => $callbackData['EventTime'] ?? 0,
                'sdk_appid' => $callbackData['SdkAppid'] ?? 0,
                'raw_data' => $callbackData
            ], 'im_callback');

            // 根据回调命令分发处理
            $result = $this->dispatchCallback($callbackData);

            // 返回处理结果
            echo json_encode($result);

        } catch (\Exception $e) {
            LogService::write('IM回调处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 'im_callback');

            echo json_encode([
                'ActionStatus' => 'FAIL',
                'ErrorCode' => 1,
                'ErrorInfo' => 'Error processing callback'
            ]);
        }
    }

    /**
     * 分发回调事件处理
     * @param array $callbackData 回调数据
     * @return array 处理结果
     */
    private function dispatchCallback($callbackData)
    {
        $command = $callbackData['CallbackCommand'] ?? '';

        switch ($command) {
            case 'State.StateChange':
                // 用户状态变更回调（上线/下线）
                $success = ImCallbackLogic::handleStateChange($callbackData);
                return $this->buildResponse($success, '用户状态变更处理');

            // case 'C2C.CallbackBeforeSendMsg':
            //     // 单聊消息发送前回调（消息过滤、权限检查）
            //     return ImCallbackLogic::handleBeforeSendMsg($callbackData);

            // case 'C2C.CallbackAfterSendMsg':
            //     // 单聊消息发送后回调（记录聊天日志、推送通知）
            //     $success = ImCallbackLogic::handleAfterSendMsg($callbackData);
            //     return $this->buildResponse($success, '消息发送后处理');

            default:
                return [
                    'ActionStatus' => 'OK',
                    'ErrorCode' => 0,
                    'ErrorInfo' => ''
                ];
        }
    }



    /**
     * 从回调数据中提取用户ID
     * @param array $callbackData 回调数据
     * @return int|string
     */
    private function extractUserId($callbackData)
    {
        $command = $callbackData['CallbackCommand'] ?? '';

        switch ($command) {
            case 'State.StateChange':
                return $callbackData['Info']['To_Account'] ?? 0;
            case 'C2C.CallbackBeforeSendMsg':
            case 'C2C.CallbackAfterSendMsg':
                return $callbackData['From_Account'] ?? 0;
            default:
                return 0;
        }
    }

    /**
     * 构建统一的响应格式
     * @param bool $success 处理是否成功
     * @param string $operation 操作描述
     * @return array
     */
    private function buildResponse($success, $operation = '')
    {
        if ($success) {
            LogService::write($operation . '成功', [], 'im_callback');
            return [
                'ActionStatus' => 'OK',
                'ErrorCode' => 0,
                'ErrorInfo' => ''
            ];
        } else {
            LogService::write($operation . '失败', [], 'im_callback');
            return [
                'ActionStatus' => 'FAIL',
                'ErrorCode' => 1,
                'ErrorInfo' => $operation . ' failed'
            ];
        }
    }
}