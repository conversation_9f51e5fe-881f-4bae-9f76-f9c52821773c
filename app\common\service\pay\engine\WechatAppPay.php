<?php

namespace app\common\service\pay\engine;

/**
 * 微信APP支付引擎
 * Class WechatAppPay
 * @package app\common\service\pay\engine
 */
class WechatAppPay extends BasePayEngine
{
    /**
     * 微信支付API地址
     */
    const API_BASE_URL = 'https://api.mch.weixin.qq.com';

    /**
     * 初始化配置
     */
    protected function initialize()
    {
        // 验证必要配置
        $requiredFields = ['app_id', 'mch_id', 'api_key', 'cert_path', 'key_path'];
        foreach ($requiredFields as $field) {
            if (empty($this->config[$field])) {
                $this->setError("微信支付配置缺少必要参数: {$field}");
                return;
            }
        }
    }

    /**
     * 统一下单
     * @param array $params 下单参数
     * @return array|false
     */
    public function unifiedOrder($params)
    {
        try {
            // 构建请求参数
            $requestData = [
                'appid' => $this->config['app_id'],
                'mch_id' => $this->config['mch_id'],
                'nonce_str' => $this->generateNonceStr(),
                'body' => $params['body'] ?? '商品购买',
                'out_trade_no' => $params['out_trade_no'],
                'total_fee' => intval($params['total_fee'] * 100), // 转换为分
                'spbill_create_ip' => $params['client_ip'] ?? request()->ip(),
                'notify_url' => $params['notify_url'],
                'trade_type' => 'APP',
            ];

            // 生成签名
            $requestData['sign'] = $this->generateSign($requestData);

            // 发送请求
            $xml = $this->arrayToXml($requestData);
            $response = $this->httpPost(self::API_BASE_URL . '/pay/unifiedorder', $xml);

            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            // 解析响应
            $result = $this->xmlToArray($response);
            if (!$result || $result['return_code'] !== 'SUCCESS') {
                $this->setError($result['return_msg'] ?? '统一下单失败');
                return false;
            }

            if ($result['result_code'] !== 'SUCCESS') {
                $this->setError($result['err_code_des'] ?? '下单失败');
                return false;
            }

            // 构建APP调起支付参数
            $appPayData = [
                'appid' => $this->config['app_id'],
                'partnerid' => $this->config['mch_id'],
                'prepayid' => $result['prepay_id'],
                'package' => 'Sign=WXPay',
                'noncestr' => $this->generateNonceStr(),
                'timestamp' => time(),
            ];
            $appPayData['sign'] = $this->generateSign($appPayData);

            $this->log('微信APP支付统一下单成功', [
                'out_trade_no' => $params['out_trade_no'],
                'prepay_id' => $result['prepay_id']
            ]);

            return [
                'prepay_id' => $result['prepay_id'],
                'app_pay_data' => $appPayData,
                'trade_no' => $result['prepay_id'],
            ];

        } catch (\Exception $e) {
            $this->setError('微信支付下单异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 查询订单
     * @param string $orderNo 订单号
     * @return array|false
     */
    public function queryOrder($orderNo)
    {
        try {
            $requestData = [
                'appid' => $this->config['app_id'],
                'mch_id' => $this->config['mch_id'],
                'out_trade_no' => $orderNo,
                'nonce_str' => $this->generateNonceStr(),
            ];

            $requestData['sign'] = $this->generateSign($requestData);
            $xml = $this->arrayToXml($requestData);
            $response = $this->httpPost(self::API_BASE_URL . '/pay/orderquery', $xml);

            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            $result = $this->xmlToArray($response);
            if (!$result || $result['return_code'] !== 'SUCCESS') {
                $this->setError($result['return_msg'] ?? '查询订单失败');
                return false;
            }

            return [
                'trade_state' => $result['trade_state'] ?? '',
                'trade_state_desc' => $result['trade_state_desc'] ?? '',
                'transaction_id' => $result['transaction_id'] ?? '',
                'out_trade_no' => $result['out_trade_no'] ?? '',
                'total_fee' => isset($result['total_fee']) ? $result['total_fee'] / 100 : 0,
            ];

        } catch (\Exception $e) {
            $this->setError('查询订单异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理支付回调
     * @param array $params 回调参数
     * @return array|false
     */
    public function handleNotify($params)
    {
        try {
            // 验证签名
            if (!$this->verifyNotify($params)) {
                $this->setError('签名验证失败');
                return false;
            }

            // 检查支付结果
            if ($params['return_code'] !== 'SUCCESS' || $params['result_code'] !== 'SUCCESS') {
                $this->setError('支付失败: ' . ($params['err_code_des'] ?? '未知错误'));
                return false;
            }

            $this->log('微信支付回调成功', [
                'out_trade_no' => $params['out_trade_no'],
                'transaction_id' => $params['transaction_id']
            ]);

            return [
                'out_trade_no' => $params['out_trade_no'],
                'transaction_id' => $params['transaction_id'],
                'total_fee' => $params['total_fee'] / 100,
                'time_end' => $params['time_end'] ?? '',
            ];

        } catch (\Exception $e) {
            $this->setError('处理回调异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 验证回调签名
     * @param array $params 回调参数
     * @return bool
     */
    public function verifyNotify($params)
    {
        if (empty($params['sign'])) {
            return false;
        }

        $sign = $params['sign'];
        unset($params['sign']);

        return $sign === $this->generateSign($params);
    }

    /**
     * 关闭订单
     * @param string $orderNo 订单号
     * @return array|false
     */
    public function closeOrder($orderNo)
    {
        try {
            $requestData = [
                'appid' => $this->config['app_id'],
                'mch_id' => $this->config['mch_id'],
                'out_trade_no' => $orderNo,
                'nonce_str' => $this->generateNonceStr(),
            ];

            $requestData['sign'] = $this->generateSign($requestData);
            $xml = $this->arrayToXml($requestData);
            $response = $this->httpPost(self::API_BASE_URL . '/pay/closeorder', $xml);

            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            $result = $this->xmlToArray($response);
            if (!$result || $result['return_code'] !== 'SUCCESS') {
                $this->setError($result['return_msg'] ?? '关闭订单失败');
                return false;
            }

            return ['result' => 'success'];

        } catch (\Exception $e) {
            $this->setError('关闭订单异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 申请退款
     * @param array $params 退款参数
     * @return array|false
     */
    public function refund($params)
    {
        try {
            $requestData = [
                'appid' => $this->config['app_id'],
                'mch_id' => $this->config['mch_id'],
                'nonce_str' => $this->generateNonceStr(),
                'out_trade_no' => $params['out_trade_no'],
                'out_refund_no' => $params['out_refund_no'],
                'total_fee' => intval($params['total_fee'] * 100),
                'refund_fee' => intval($params['refund_fee'] * 100),
                'refund_desc' => $params['refund_desc'] ?? '订单退款',
            ];

            $requestData['sign'] = $this->generateSign($requestData);
            $xml = $this->arrayToXml($requestData);
            
            // 退款需要使用证书
            $response = $this->httpPostWithCert(self::API_BASE_URL . '/secapi/pay/refund', $xml);

            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            $result = $this->xmlToArray($response);
            if (!$result || $result['return_code'] !== 'SUCCESS') {
                $this->setError($result['return_msg'] ?? '申请退款失败');
                return false;
            }

            if ($result['result_code'] !== 'SUCCESS') {
                $this->setError($result['err_code_des'] ?? '退款失败');
                return false;
            }

            return [
                'refund_id' => $result['refund_id'],
                'out_refund_no' => $result['out_refund_no'],
                'refund_fee' => $result['refund_fee'] / 100,
            ];

        } catch (\Exception $e) {
            $this->setError('申请退款异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 查询退款
     * @param string $refundNo 退款单号
     * @return array|false
     */
    public function queryRefund($refundNo)
    {
        try {
            $requestData = [
                'appid' => $this->config['app_id'],
                'mch_id' => $this->config['mch_id'],
                'out_refund_no' => $refundNo,
                'nonce_str' => $this->generateNonceStr(),
            ];

            $requestData['sign'] = $this->generateSign($requestData);
            $xml = $this->arrayToXml($requestData);
            $response = $this->httpPost(self::API_BASE_URL . '/pay/refundquery', $xml);

            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            $result = $this->xmlToArray($response);
            if (!$result || $result['return_code'] !== 'SUCCESS') {
                $this->setError($result['return_msg'] ?? '查询退款失败');
                return false;
            }

            return [
                'refund_status_0' => $result['refund_status_0'] ?? '',
                'refund_fee_0' => isset($result['refund_fee_0']) ? $result['refund_fee_0'] / 100 : 0,
            ];

        } catch (\Exception $e) {
            $this->setError('查询退款异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取支付引擎名称
     * @return string
     */
    public function getEngineName()
    {
        return '微信APP支付';
    }

    /**
     * 生成签名
     * @param array $params 参数数组
     * @return string
     */
    protected function generateSign($params)
    {
        // 过滤空值并排序
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });
        ksort($params);

        // 构建签名字符串
        $stringA = '';
        foreach ($params as $key => $value) {
            $stringA .= $key . '=' . $value . '&';
        }
        $stringSignTemp = $stringA . 'key=' . $this->config['api_key'];

        return strtoupper(md5($stringSignTemp));
    }

    /**
     * 数组转XML
     * @param array $data 数据数组
     * @return string
     */
    protected function arrayToXml($data)
    {
        $xml = '<xml>';
        foreach ($data as $key => $value) {
            $xml .= '<' . $key . '><![CDATA[' . $value . ']]></' . $key . '>';
        }
        $xml .= '</xml>';
        return $xml;
    }

    /**
     * XML转数组
     * @param string $xml XML字符串
     * @return array|false
     */
    protected function xmlToArray($xml)
    {
        try {
            $data = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
            return json_decode(json_encode($data), true);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * HTTP POST请求
     * @param string $url 请求地址
     * @param string $data 请求数据
     * @return string|false
     */
    protected function httpPost($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return false;
        }

        return $response;
    }

    /**
     * 带证书的HTTP POST请求
     * @param string $url 请求地址
     * @param string $data 请求数据
     * @return string|false
     */
    protected function httpPostWithCert($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        // 设置证书
        curl_setopt($ch, CURLOPT_SSLCERT, $this->config['cert_path']);
        curl_setopt($ch, CURLOPT_SSLKEY, $this->config['key_path']);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return false;
        }

        return $response;
    }
}
