<?php

namespace app\applent\validate\gift;

use app\common\validate\BaseValidate;

/**
 * 礼物赠送验证器
 * Class GiftSendValidate
 * @package app\applent\validate\gift
 */
class GiftSendValidate extends BaseValidate
{
    protected $rule = [
        'gift_id' => 'require|integer|gt:0',
        'gift_count' => 'require|integer|between:1,999',
        'source_type' => 'require|integer|in:1,2,3,4',
        'to_user_id' => 'require|integer|gt:0',
        'source_id' => 'max:50',
    ];

    protected $message = [
        'gift_id.require' => '请选择礼物',
        'gift_id.integer' => '礼物ID必须为整数',
        'gift_id.gt' => '礼物ID必须大于0',
        'gift_count.require' => '请输入礼物数量',
        'gift_count.integer' => '礼物数量必须为整数',
        'gift_count.between' => '礼物数量必须在1-999之间',
        'source_type.require' => '请指定来源类型',
        'source_type.integer' => '来源类型必须为整数',
        'source_type.in' => '来源类型不正确，必须为1-4之间的值',
        'to_user_id.require' => '请指定接收用户',
        'to_user_id.integer' => '接收用户ID必须为整数',
        'to_user_id.gt' => '接收用户ID必须大于0',
        'source_id.max' => '来源ID长度不能超过50个字符',
    ];

    /**
     * 赠送礼物场景
     */
    public function sceneSendGift()
    {
        return $this->only(['gift_id', 'gift_count', 'source_type', 'to_user_id', 'source_id']);
    }
}
