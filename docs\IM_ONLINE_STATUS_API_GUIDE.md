# 腾讯云IM用户在线状态查询接口

## 🎯 功能说明

已为项目添加了完整的用户在线状态查询功能，支持单个用户查询和批量用户查询。

## 📋 新增的方法

### 1. TencentImService类新增方法

#### 1.1 queryOnlineStatus() - 查询用户在线状态
```php
/**
 * 查询用户在线状态
 * @param string|array $userIds 用户ID（字符串）或用户ID数组
 * @return array 在线状态查询结果
 */
public function queryOnlineStatus($userIds)
```

#### 1.2 isUserOnline() - 检查单个用户是否在线
```php
/**
 * 检查单个用户是否在线
 * @param string $userId 用户ID
 * @return bool 是否在线
 */
public function isUserOnline($userId)
```

#### 1.3 batchCheckOnlineStatus() - 批量检查用户在线状态
```php
/**
 * 批量检查用户在线状态
 * @param array $userIds 用户ID数组
 * @return array 在线用户ID数组
 */
public function batchCheckOnlineStatus($userIds)
```

### 2. im_common.php新增便捷函数

#### 2.1 im_check_user_online_state() - 查询用户在线状态（完整结果）
```php
/**
 * 查询用户在线状态
 * @param int $user_id 用户ID
 * @return array 在线状态结果
 */
function im_check_user_online_state($user_id)
```

#### 2.2 check_user_online() - 检查用户是否在线（简化版）
```php
/**
 * 检查用户是否在线（简化版）
 * @param int $user_id 用户ID
 * @return bool 是否在线
 */
function check_user_online($user_id)
```

#### 2.3 batch_check_users_online() - 批量检查用户在线状态
```php
/**
 * 批量检查用户在线状态
 * @param array $user_ids 用户ID数组
 * @return array 在线用户ID数组
 */
function batch_check_users_online($user_ids)
```

## 🚀 使用示例

### 1. 检查单个用户是否在线

```php
// 方法一：使用便捷函数（推荐）
$isOnline = check_user_online(1001);
if ($isOnline) {
    echo "用户1001在线";
} else {
    echo "用户1001离线";
}

// 方法二：获取完整查询结果
$result = im_check_user_online_state(1001);
if ($result['ErrorCode'] === 0 && !empty($result['QueryResult'])) {
    $userStatus = $result['QueryResult'][0];
    echo "用户状态: " . $userStatus['State']; // Online 或 Offline
} else {
    echo "查询失败: " . $result['ErrorInfo'];
}

// 方法三：直接使用服务类
use app\common\service\tim\TencentImService;
$imService = new TencentImService();
$isOnline = $imService->isUserOnline('1001');
```

### 2. 批量检查用户在线状态

```php
$userIds = [1001, 1002, 1003, 1004, 1005];

// 方法一：使用便捷函数
$onlineUsers = batch_check_users_online($userIds);
echo "在线用户: " . implode(', ', $onlineUsers);
// 输出: 在线用户: 1001, 1003, 1005

// 方法二：使用服务类
use app\common\service\tim\TencentImService;
$imService = new TencentImService();
$onlineUsers = $imService->batchCheckOnlineStatus($userIds);
```

### 3. 在控制器中使用

```php
class UserController
{
    /**
     * 获取用户列表（带在线状态）
     */
    public function getUserList()
    {
        $users = User::field('user_id,nickname,avatar')->limit(20)->select();
        
        // 批量检查在线状态
        $userIds = array_column($users->toArray(), 'user_id');
        $onlineUsers = batch_check_users_online($userIds);
        
        // 添加在线状态标记
        foreach ($users as &$user) {
            $user['is_online'] = in_array($user['user_id'], $onlineUsers);
        }
        
        return json(['code' => 1, 'data' => $users]);
    }

    /**
     * 发起视频通话前检查
     */
    public function startVideoCall()
    {
        $targetUserId = input('target_user_id');
        
        // 检查对方是否在线
        if (!check_user_online($targetUserId)) {
            return json(['code' => 0, 'msg' => '对方当前不在线']);
        }
        
        // 继续通话逻辑...
        return json(['code' => 1, 'msg' => '通话发起成功']);
    }
}
```

## 📊 API响应格式

### 1. queryOnlineStatus() 完整响应
```json
{
    "ActionStatus": "OK",
    "ErrorCode": 0,
    "ErrorInfo": "",
    "QueryResult": [
        {
            "To_Account": "1001",
            "State": "Online"
        },
        {
            "To_Account": "1002", 
            "State": "Offline"
        }
    ]
}
```

### 2. 用户状态说明
- **Online**: 用户在线
- **Offline**: 用户离线
- **PushOnline**: 用户在线但应用在后台（iOS推送在线状态）

### 3. 错误响应
```json
{
    "ActionStatus": "FAIL",
    "ErrorCode": 70107,
    "ErrorInfo": "Identifier invalid"
}
```

## ⚠️ 注意事项

### 1. 调用频率限制
- 腾讯云IM对查询在线状态接口有调用频率限制
- 建议合理使用缓存，避免频繁调用
- 批量查询比单个查询更高效

### 2. 用户ID格式
- 腾讯云IM要求用户ID为字符串格式
- 代码中已自动处理类型转换

### 3. 异常处理
- 所有方法都包含完善的异常处理
- 网络异常或API异常时会记录日志
- 异常情况下返回安全的默认值

### 4. 日志记录
- 所有API调用都会记录详细日志
- 调试日志记录在 `im_api_debug` 分类
- 错误日志记录在 `im_api` 分类

## 🔧 配置要求

确保以下配置正确设置：
- `tencent_sdkappid`: 腾讯云IM应用ID
- `tencent_identifier`: 管理员账号
- `tencent_secret_key`: 密钥

## 🎯 最佳实践

### 1. 缓存策略
```php
function checkUserOnlineWithCache($userId) {
    // 先检查缓存
    $cacheKey = "user_online_status_{$userId}";
    $cached = Cache::get($cacheKey);
    
    if ($cached !== null) {
        return $cached;
    }
    
    // 缓存未命中，查询IM服务器
    $isOnline = check_user_online($userId);
    
    // 缓存结果（5分钟）
    Cache::set($cacheKey, $isOnline, 300);
    
    return $isOnline;
}
```

### 2. 批量查询优化
```php
function batchCheckWithPagination($userIds, $pageSize = 100) {
    $pages = array_chunk($userIds, $pageSize);
    $allOnlineUsers = [];
    
    foreach ($pages as $pageUsers) {
        $onlineUsers = batch_check_users_online($pageUsers);
        $allOnlineUsers = array_merge($allOnlineUsers, $onlineUsers);
        
        // 避免频繁调用
        usleep(100000); // 休息0.1秒
    }
    
    return $allOnlineUsers;
}
```

### 3. 错误处理
```php
function safeCheckUserOnline($userId) {
    try {
        return check_user_online($userId);
    } catch (\Exception $e) {
        // 记录错误但不影响业务流程
        LogService::write('在线状态查询失败', [
            'user_id' => $userId,
            'error' => $e->getMessage()
        ], 'user_online_error');
        
        // 返回保守的结果
        return false;
    }
}
```

现在您可以使用这些接口来准确查询用户的真实在线状态了！
