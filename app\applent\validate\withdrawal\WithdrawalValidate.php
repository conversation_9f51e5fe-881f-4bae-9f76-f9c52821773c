<?php

namespace app\applent\validate\withdrawal;

use app\common\validate\BaseValidate;

/**
 * 提现验证器
 * Class WithdrawalValidate
 * @package app\applent\validate\withdrawal
 */
class WithdrawalValidate extends BaseValidate
{
    protected $rule = [
        'config_id' => 'require|integer|gt:0',
        'withdrawal_amount' => 'require|float|gt:0',
        'exchange_amount' => 'require|float|gt:0',
        'withdrawal_type' => 'require|integer|in:1,2',
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:1,100',
        'date' => 'checkDateFormat',
        'status' => 'integer|in:0,1,2,3',
    ];

    protected $message = [
        'config_id.require' => '请选择提现金额',
        'config_id.integer' => '提现配置ID必须为整数',
        'config_id.gt' => '提现配置ID必须大于0',
        'withdrawal_amount.require' => '请输入提现金额',
        'withdrawal_amount.float' => '提现金额必须为数字',
        'withdrawal_amount.gt' => '提现金额必须大于0',
        'exchange_amount.require' => '请输入兑换点数',
        'exchange_amount.float' => '兑换点数必须为数字',
        'exchange_amount.gt' => '兑换点数必须大于0',
        'withdrawal_type.require' => '请选择提现方式',
        'withdrawal_type.integer' => '提现方式必须为整数',
        'withdrawal_type.in' => '提现方式只能是银行卡(1)或支付宝(2)',
        'page.integer' => '页码必须为整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须为整数',
        'limit.between' => '每页数量必须在1-100之间',
        'date.checkDateFormat' => '日期格式错误',
        'status.integer' => '状态必须为整数',
        'status.in' => '状态值错误',
    ];

    /**
     * @notes 申请提现场景
     * @return WithdrawalValidate
     */
    public function sceneApplyWithdrawal()
    {
        return $this->only(['config_id', 'withdrawal_amount', 'exchange_amount', 'withdrawal_type']);
    }

    /**
     * @notes 提现记录列表场景
     * @return WithdrawalValidate
     */
    public function sceneWithdrawalList()
    {
        return $this->only(['page', 'limit', 'date', 'status']);
    }

    /**
     * @notes 自定义验证日期格式
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     */
    protected function checkDateFormat($value, $rule, $data)
    {
        if (empty($value)) {
            return true; // 允许为空
        }

        // 验证格式是否为 YYYY-MM
        if (!preg_match('/^\d{4}-\d{2}$/', $value)) {
            return '日期格式错误，请使用YYYY-MM格式';
        }

        // 验证日期是否有效
        $parts = explode('-', $value);
        $year = (int)$parts[0];
        $month = (int)$parts[1];

        if ($year < 2020 || $year > 2030) {
            return '年份必须在2020-2030之间';
        }

        if ($month < 1 || $month > 12) {
            return '月份必须在01-12之间';
        }

        return true;
    }
}
