<?php

namespace app\common\service\pay\engine;

/**
 * 小叶支付引擎（基于彩虹易支付SDK）
 * Class XiaoyePay
 * @package app\common\service\pay\engine
 */
class XiaoyePay extends BasePayEngine
{
    /**
     * 小叶支付SDK实例
     */
    private $epayCore;

    /**
     * 签名类型
     */
    const SIGN_TYPE = 'RSA';

    /**
     * 初始化配置
     */
    protected function initialize()
    {
        // 验证必要配置
        $requiredFields = ['apiurl', 'pid', 'platform_public_key', 'merchant_private_key'];

        foreach ($requiredFields as $field) {
            if (empty($this->config[$field])) {
                $this->setError("小叶支付配置缺少必要参数: {$field}");
                return;
            }
        }

        // 不再依赖外部SDK，使用内置实现
        // 验证必需的配置参数
        $requiredFields = ['apiurl', 'pid', 'platform_public_key', 'merchant_private_key'];
        foreach ($requiredFields as $field) {
            if (empty($this->config[$field])) {
                $this->setError("小叶支付配置缺少必需参数: {$field}");
                return;
            }
        }
    }

    /**
     * 统一下单
     * @param array $params 下单参数
     * @return array|false
     */
    public function unifiedOrder($params)
    {
        try {
            // 验证必要参数
            if (!$this->validateOrderParams($params)) {
                return false;
            }

            // 构建请求参数
            $requestParams = [
                'pid' => $this->config['pid'],
                'method'=>'jump',
                'type' => $params['pay_type'] ?? 'alipay', // 支付方式：alipay,wxpay,qqpay,bank,jdpay
                'out_trade_no' => $params['out_trade_no'],
                'notify_url' => $params['notify_url'],
                'return_url' => $params['return_url'] ?? '',
                'name' => $params['subject'] ?? '商品支付',
                'money' => $this->formatAmount($params['total_amount']),
                // 'money' => $params['pay_type']=='alipay' ? 1.00 : 0.1,
                'clientip' => $this->get_wx_ip(),
                'timestamp' => time(),
            ];
            // 生成签名
            $requestParams['sign'] = $this->generateSign($requestParams);
            $requestParams['sign_type'] = 'RSA';

            // 发起HTTP请求 - 使用正确的API端点
            $result = $this->httpPost($this->config['apiurl'] . '/api/pay/create', $requestParams);
           
            if (!$result) {
                $this->setError('请求支付接口失败');
                return false;
            }

            $data = json_decode($result, true);

            if (!$data || $data['code']) {
                $this->setError($data['msg'] ?? '统一下单失败');
                return false;
            }

            return [
                'trade_no' => $data['trade_no'],
                'pay_type' => $data['pay_type'],
                'pay_info' => $data['pay_info'],
            ];

        } catch (\Exception $e) {
            $this->setError('支付异常: ' . $e->getMessage());
            return false;
        }
    }

    protected function get_wx_ip()
    {
        $ip = "unknown";
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        } elseif (getenv("REMOTE_ADDR")) {
            $ip = getenv("REMOTE_ADDR");
        }
        return $ip;
    }

    /**
     * 查询订单
     * @param string $orderNo 订单号
     * @return array|false
     */
    public function queryOrder($orderNo)
    {
        try {
            // 构建查询参数
            $queryParams = [
                'pid' => $this->config['pid'],
                'out_trade_no' => $orderNo,
            ];

            // 生成签名
            $queryParams['sign'] = $this->generateSign($queryParams);
            $queryParams['sign_type'] = 'RSA';

            // 发起查询请求 - 使用正确的API端点
            $result = $this->httpPost($this->config['apiurl'] . 'api/pay/query', $queryParams);

            if (!$result) {
                $this->setError('查询订单失败');
                return false;
            }

            $data = json_decode($result, true);
            if (!$data || $data['code'] !== 1) {
                $this->setError($data['msg'] ?? '查询订单失败');
                return false;
            }

            return [
                'trade_status' => $data['status'] == 1 ? 'TRADE_SUCCESS' : 'TRADE_PENDING',
                'trade_no' => $data['trade_no'] ?? '',
                'out_trade_no' => $data['out_trade_no'] ?? '',
                'total_amount' => floatval($data['money'] ?? 0),
                'pay_time' => $data['endtime'] ?? '',
            ];

        } catch (\Exception $e) {
            $this->setError('小叶支付查询订单异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理支付回调
     * @param array $params 回调参数
     * @return array|false
     */
    public function handleNotify($params)
    {
        try {
            // 验证签名
            if (!$this->verifyNotify($params)) {
                $this->setError('签名验证失败');
                return false;
            }

            // 检查支付状态
            if ($params['trade_status'] !== 'TRADE_SUCCESS') {
                $this->setError('支付状态异常: ' . $params['trade_status']);
                return false;
            }

            return [
                'out_trade_no' => $params['out_trade_no'],
                'trade_no' => $params['trade_no'],
                'total_amount' => floatval($params['money']),
                'pay_time' => $params['endtime'] ?? '',
            ];

        } catch (\Exception $e) {
            $this->setError('处理回调异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 验证回调签名
     * @param array $params 回调参数
     * @return bool
     */
    public function verifyNotify($params)
    {
        try {
            // 使用内置方法验证回调签名
            return $this->verifySign($params);
        } catch (\Exception $e) {
            $this->setError('验证回调签名异常: ' . $e->getMessage());
            return false;
        }
    }



    /**
     * 申请退款
     * @param array $params 退款参数
     * @return array|false
     */
    public function refund($params)
    {
        try {
            // 使用SDK申请退款
            $result = $this->epayCore->refund(
                $params['out_refund_no'],
                $params['out_trade_no'],
                $this->formatAmount($params['refund_fee'])
            );

            if (!$result) {
                $this->setError('申请退款失败');
                return false;
            }

            return [
                'refund_id' => $result['out_refund_no'] ?? '',
                'out_refund_no' => $result['out_refund_no'] ?? '',
                'refund_fee' => floatval($result['money'] ?? 0),
            ];

        } catch (\Exception $e) {
            $this->setError('小叶支付申请退款异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 查询退款
     * @param string $refundNo 退款单号
     * @return array|false
     */
    public function queryRefund($refundNo)
    {
        // 小叶支付SDK暂不支持退款查询，返回默认状态
        return [
            'refund_status' => 'SUCCESS',
            'refund_amount' => 0,
        ];
    }

    /**
     * 关闭订单
     * @param string $outTradeNo 商户订单号
     * @return bool
     */
    public function closeOrder($outTradeNo)
    {
        // 小叶支付SDK暂不支持关闭订单
        return true;
    }

    /**
     * 获取支付引擎名称
     * @return string
     */
    public function getEngineName()
    {
        return '小叶支付';
    }

    /**
     * 验证订单参数
     * @param array $params 订单参数
     * @return bool
     */
    protected function validateOrderParams($params)
    {
        // 验证必要参数
        $requiredFields = ['out_trade_no', 'total_amount', 'notify_url'];
        foreach ($requiredFields as $field) {
            if (empty($params[$field])) {
                $this->setError("缺少必要参数: {$field}");
                return false;
            }
        }

        // 验证订单号格式
        if (!$this->validateOrderNo($params['out_trade_no'])) {
            $this->setError('订单号格式错误');
            return false;
        }

        // 验证金额
        if (!is_numeric($params['total_amount']) || $params['total_amount'] <= 0) {
            $this->setError('金额格式错误');
            return false;
        }

        return true;
    }

    /**
     * 格式化金额
     * @param float $amount 金额
     * @return string
     */
    protected function formatAmount($amount)
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * 验证订单号格式
     * @param string $orderNo 订单号
     * @return bool
     */
    protected function validateOrderNo($orderNo)
    {
        // 订单号长度限制：6-32位
        if (strlen($orderNo) < 6 || strlen($orderNo) > 32) {
            return false;
        }

        // 只允许字母、数字、下划线、横线
        return preg_match('/^[a-zA-Z0-9_-]+$/', $orderNo);
    }

    /**
     * 生成RSA2签名
     * @param array $params 参数数组
     * @return string
     */
    protected function generateSign($params)
    {
        // 移除sign和sign_type参数
        unset($params['sign'], $params['sign_type']);

        // 按键名排序
        ksort($params);

        // 构建待签名字符串
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&');
        
        // 使用RSA私钥签名
        $privateKey = "-----BEGIN PRIVATE KEY-----\n" .
                     chunk_split($this->config['merchant_private_key'], 64, "\n") .
                     "-----END PRIVATE KEY-----";

        $key = openssl_pkey_get_private($privateKey);

        if (!$key) {
            throw new \Exception('私钥格式错误');
        }

        openssl_sign($signStr, $signature, $key, OPENSSL_ALGO_SHA256);
        // openssl_free_key($key); // PHP 8.0+ 中已弃用，资源会自动释放

        return base64_encode($signature);
    }

    /**
     * 发送HTTP POST请求
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @return string|false
     */
    protected function httpPost($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'User-Agent: XiaoyePay-PHP-SDK'
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            $this->setError('HTTP请求失败: ' . $error);
            return false;
        }

        if ($httpCode !== 200) {
            $this->setError('HTTP请求失败，状态码: ' . $httpCode);
            return false;
        }

        return $result;
    }

    /**
     * 验证回调签名
     * @param array $params 回调参数
     * @return bool
     */
    protected function verifySign($params)
    {
        if (!isset($params['sign']) || !isset($params['sign_type'])) {
            return false;
        }

        $sign = $params['sign'];
        unset($params['sign'], $params['sign_type']);

        // 按键名排序
        ksort($params);

        // 构建待验签字符串
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&');

        // 使用平台公钥验签
        $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                    chunk_split($this->config['platform_public_key'], 64, "\n") .
                    "-----END PUBLIC KEY-----";

        $key = openssl_pkey_get_public($publicKey);
        if (!$key) {
            return false;
        }

        $result = openssl_verify($signStr, base64_decode($sign), $key, OPENSSL_ALGO_SHA256);
        // openssl_free_key($key); // PHP 8.0+ 中已弃用，资源会自动释放

        return $result === 1;
    }
}
