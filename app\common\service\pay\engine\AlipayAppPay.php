<?php

namespace app\common\service\pay\engine;

/**
 * 支付宝APP支付引擎
 * Class AlipayAppPay
 * @package app\common\service\pay\engine
 */
class AlipayAppPay extends BasePayEngine
{
    /**
     * 支付宝网关地址
     */
    const GATEWAY_URL = 'https://openapi.alipay.com/gateway.do';
    const SANDBOX_GATEWAY_URL = 'https://openapi.alipaydev.com/gateway.do';

    /**
     * 初始化配置
     */
    protected function initialize()
    {
        // 验证必要配置
        $requiredFields = ['app_id', 'private_key', 'public_key', 'alipay_public_key'];
        foreach ($requiredFields as $field) {
            if (empty($this->config[$field])) {
                $this->setError("支付宝配置缺少必要参数: {$field}");
                return;
            }
        }
    }

    /**
     * 统一下单
     * @param array $params 下单参数
     * @return array|false
     */
    public function unifiedOrder($params)
    {
        try {
            // 构建业务参数
            $bizContent = [
                'out_trade_no' => $params['out_trade_no'],
                'total_amount' => number_format($params['total_fee'], 2, '.', ''),
                'subject' => $params['body'] ?? '商品购买',
                'product_code' => 'QUICK_MSECURITY_PAY',
                'timeout_express' => '30m',
            ];

            // 构建请求参数
            $requestParams = [
                'app_id' => $this->config['app_id'],
                'method' => 'alipay.trade.app.pay',
                'format' => 'JSON',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'notify_url' => $params['notify_url'],
                'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE),
            ];

            // 生成签名
            $requestParams['sign'] = $this->generateSign($requestParams);

            // 构建APP调起支付的字符串
            $orderString = $this->buildOrderString($requestParams);

            $this->log('支付宝APP支付统一下单成功', [
                'out_trade_no' => $params['out_trade_no'],
                'total_amount' => $bizContent['total_amount']
            ]);

            return [
                'order_string' => $orderString,
                'trade_no' => $params['out_trade_no'],
            ];

        } catch (\Exception $e) {
            $this->setError('支付宝下单异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 查询订单
     * @param string $orderNo 订单号
     * @return array|false
     */
    public function queryOrder($orderNo)
    {
        try {
            $bizContent = [
                'out_trade_no' => $orderNo,
            ];

            $requestParams = [
                'app_id' => $this->config['app_id'],
                'method' => 'alipay.trade.query',
                'format' => 'JSON',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE),
            ];

            $requestParams['sign'] = $this->generateSign($requestParams);

            $response = $this->httpPost($this->getGatewayUrl(), $requestParams);
            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            $result = json_decode($response, true);
            if (!$result) {
                $this->setError('响应数据解析失败');
                return false;
            }

            $responseKey = 'alipay_trade_query_response';
            if (!isset($result[$responseKey])) {
                $this->setError('响应格式错误');
                return false;
            }

            $tradeInfo = $result[$responseKey];
            if ($tradeInfo['code'] !== '10000') {
                $this->setError($tradeInfo['msg'] ?? '查询订单失败');
                return false;
            }

            return [
                'trade_status' => $tradeInfo['trade_status'] ?? '',
                'trade_no' => $tradeInfo['trade_no'] ?? '',
                'out_trade_no' => $tradeInfo['out_trade_no'] ?? '',
                'total_amount' => floatval($tradeInfo['total_amount'] ?? 0),
            ];

        } catch (\Exception $e) {
            $this->setError('查询订单异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理支付回调
     * @param array $params 回调参数
     * @return array|false
     */
    public function handleNotify($params)
    {
        try {
            // 验证签名
            if (!$this->verifyNotify($params)) {
                $this->setError('签名验证失败');
                return false;
            }

            // 检查交易状态
            if (!in_array($params['trade_status'], ['TRADE_SUCCESS', 'TRADE_FINISHED'])) {
                $this->setError('交易状态异常: ' . $params['trade_status']);
                return false;
            }

            $this->log('支付宝支付回调成功', [
                'out_trade_no' => $params['out_trade_no'],
                'trade_no' => $params['trade_no']
            ]);

            return [
                'out_trade_no' => $params['out_trade_no'],
                'trade_no' => $params['trade_no'],
                'total_amount' => floatval($params['total_amount']),
                'gmt_payment' => $params['gmt_payment'] ?? '',
            ];

        } catch (\Exception $e) {
            $this->setError('处理回调异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 验证回调签名
     * @param array $params 回调参数
     * @return bool
     */
    public function verifyNotify($params)
    {
        if (empty($params['sign']) || empty($params['sign_type'])) {
            return false;
        }

        $sign = $params['sign'];
        $signType = $params['sign_type'];
        unset($params['sign'], $params['sign_type']);

        // 构建待签名字符串
        $signString = $this->buildSignString($params);

        // 验证签名
        if ($signType === 'RSA2') {
            return $this->verifyRSA2($signString, $sign);
        } elseif ($signType === 'RSA') {
            return $this->verifyRSA($signString, $sign);
        }

        return false;
    }

    /**
     * 关闭订单
     * @param string $orderNo 订单号
     * @return array|false
     */
    public function closeOrder($orderNo)
    {
        try {
            $bizContent = [
                'out_trade_no' => $orderNo,
            ];

            $requestParams = [
                'app_id' => $this->config['app_id'],
                'method' => 'alipay.trade.close',
                'format' => 'JSON',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE),
            ];

            $requestParams['sign'] = $this->generateSign($requestParams);

            $response = $this->httpPost($this->getGatewayUrl(), $requestParams);
            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            $result = json_decode($response, true);
            if (!$result) {
                $this->setError('响应数据解析失败');
                return false;
            }

            $responseKey = 'alipay_trade_close_response';
            if (!isset($result[$responseKey])) {
                $this->setError('响应格式错误');
                return false;
            }

            $closeInfo = $result[$responseKey];
            if ($closeInfo['code'] !== '10000') {
                $this->setError($closeInfo['msg'] ?? '关闭订单失败');
                return false;
            }

            return ['result' => 'success'];

        } catch (\Exception $e) {
            $this->setError('关闭订单异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 申请退款
     * @param array $params 退款参数
     * @return array|false
     */
    public function refund($params)
    {
        try {
            $bizContent = [
                'out_trade_no' => $params['out_trade_no'],
                'refund_amount' => number_format($params['refund_fee'], 2, '.', ''),
                'out_request_no' => $params['out_refund_no'],
                'refund_reason' => $params['refund_desc'] ?? '订单退款',
            ];

            $requestParams = [
                'app_id' => $this->config['app_id'],
                'method' => 'alipay.trade.refund',
                'format' => 'JSON',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE),
            ];

            $requestParams['sign'] = $this->generateSign($requestParams);

            $response = $this->httpPost($this->getGatewayUrl(), $requestParams);
            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            $result = json_decode($response, true);
            if (!$result) {
                $this->setError('响应数据解析失败');
                return false;
            }

            $responseKey = 'alipay_trade_refund_response';
            if (!isset($result[$responseKey])) {
                $this->setError('响应格式错误');
                return false;
            }

            $refundInfo = $result[$responseKey];
            if ($refundInfo['code'] !== '10000') {
                $this->setError($refundInfo['msg'] ?? '申请退款失败');
                return false;
            }

            return [
                'trade_no' => $refundInfo['trade_no'],
                'out_trade_no' => $refundInfo['out_trade_no'],
                'refund_fee' => floatval($refundInfo['refund_fee']),
            ];

        } catch (\Exception $e) {
            $this->setError('申请退款异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 查询退款
     * @param string $refundNo 退款单号
     * @return array|false
     */
    public function queryRefund($refundNo)
    {
        try {
            $bizContent = [
                'out_request_no' => $refundNo,
            ];

            $requestParams = [
                'app_id' => $this->config['app_id'],
                'method' => 'alipay.trade.fastpay.refund.query',
                'format' => 'JSON',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE),
            ];

            $requestParams['sign'] = $this->generateSign($requestParams);

            $response = $this->httpPost($this->getGatewayUrl(), $requestParams);
            if (!$response) {
                $this->setError('网络请求失败');
                return false;
            }

            $result = json_decode($response, true);
            if (!$result) {
                $this->setError('响应数据解析失败');
                return false;
            }

            $responseKey = 'alipay_trade_fastpay_refund_query_response';
            if (!isset($result[$responseKey])) {
                $this->setError('响应格式错误');
                return false;
            }

            $refundInfo = $result[$responseKey];
            if ($refundInfo['code'] !== '10000') {
                $this->setError($refundInfo['msg'] ?? '查询退款失败');
                return false;
            }

            return [
                'refund_status' => $refundInfo['refund_status'] ?? '',
                'refund_amount' => floatval($refundInfo['refund_amount'] ?? 0),
            ];

        } catch (\Exception $e) {
            $this->setError('查询退款异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取支付引擎名称
     * @return string
     */
    public function getEngineName()
    {
        return '支付宝APP支付';
    }

    /**
     * 获取网关地址
     * @return string
     */
    protected function getGatewayUrl()
    {
        return isset($this->config['sandbox']) && $this->config['sandbox']
            ? self::SANDBOX_GATEWAY_URL
            : self::GATEWAY_URL;
    }

    /**
     * 生成签名
     * @param array $params 参数数组
     * @return string
     */
    protected function generateSign($params)
    {
        // 构建待签名字符串
        $signString = $this->buildSignString($params);

        // 使用RSA2签名
        return $this->signRSA2($signString);
    }

    /**
     * 构建签名字符串
     * @param array $params 参数数组
     * @return string
     */
    protected function buildSignString($params)
    {
        // 过滤空值并排序
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });
        ksort($params);

        $stringToBeSigned = '';
        foreach ($params as $key => $value) {
            $stringToBeSigned .= $key . '=' . $value . '&';
        }

        return rtrim($stringToBeSigned, '&');
    }

    /**
     * 构建订单字符串
     * @param array $params 参数数组
     * @return string
     */
    protected function buildOrderString($params)
    {
        $orderString = '';
        foreach ($params as $key => $value) {
            $orderString .= $key . '=' . urlencode($value) . '&';
        }

        return rtrim($orderString, '&');
    }

    /**
     * RSA2签名
     * @param string $data 待签名数据
     * @return string
     */
    protected function signRSA2($data)
    {
        $privateKey = $this->formatPrivateKey($this->config['private_key']);
        $res = openssl_pkey_get_private($privateKey);
        if (!$res) {
            throw new \Exception('私钥格式错误');
        }

        openssl_sign($data, $sign, $res, OPENSSL_ALGO_SHA256);
        openssl_free_key($res);

        return base64_encode($sign);
    }

    /**
     * RSA2验签
     * @param string $data 原始数据
     * @param string $sign 签名
     * @return bool
     */
    protected function verifyRSA2($data, $sign)
    {
        $publicKey = $this->formatPublicKey($this->config['alipay_public_key']);
        $res = openssl_pkey_get_public($publicKey);
        if (!$res) {
            return false;
        }

        $result = openssl_verify($data, base64_decode($sign), $res, OPENSSL_ALGO_SHA256);
        openssl_free_key($res);

        return $result === 1;
    }

    /**
     * RSA验签
     * @param string $data 原始数据
     * @param string $sign 签名
     * @return bool
     */
    protected function verifyRSA($data, $sign)
    {
        $publicKey = $this->formatPublicKey($this->config['alipay_public_key']);
        $res = openssl_pkey_get_public($publicKey);
        if (!$res) {
            return false;
        }

        $result = openssl_verify($data, base64_decode($sign), $res, OPENSSL_ALGO_SHA1);
        openssl_free_key($res);

        return $result === 1;
    }

    /**
     * 格式化私钥
     * @param string $privateKey 私钥
     * @return string
     */
    protected function formatPrivateKey($privateKey)
    {
        if (strpos($privateKey, '-----BEGIN') !== false) {
            return $privateKey;
        }

        $privateKey = chunk_split($privateKey, 64, "\n");
        return "-----BEGIN RSA PRIVATE KEY-----\n" . $privateKey . "-----END RSA PRIVATE KEY-----\n";
    }

    /**
     * 格式化公钥
     * @param string $publicKey 公钥
     * @return string
     */
    protected function formatPublicKey($publicKey)
    {
        if (strpos($publicKey, '-----BEGIN') !== false) {
            return $publicKey;
        }

        $publicKey = chunk_split($publicKey, 64, "\n");
        return "-----BEGIN PUBLIC KEY-----\n" . $publicKey . "-----END PUBLIC KEY-----\n";
    }

    /**
     * HTTP POST请求
     * @param string $url 请求地址
     * @param array $data 请求数据
     * @return string|false
     */
    protected function httpPost($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded;charset=utf-8'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return false;
        }

        return $response;
    }
}
