<?php

namespace app\applent\logic\crontab;

use app\common\logic\BaseLogic;
use app\common\model\recharge\RechargeOrder;
use think\facade\Db;

/**
 * 定时任务逻辑类
 * Class CrontabLogic
 * @package app\applent\logic\crontab
 */
class CrontabLogic extends BaseLogic
{
    /**
     * @notes 处理超时充值订单
     * @return array
     */
    public static function handleTimeoutRechargeOrders()
    {
        try {
            // 开启事务
            Db::startTrans();
            // 直接根据条件批量更新订单状态为超时
            $processedCount = RechargeOrder::where([
                ['pay_status', '=', 0], // 未支付
                ['create_time', '<', time() - 600], // 创建时间超过10分钟
            ])->update([
                'pay_status' => 1, // 超时状态
                'update_time' => time()
            ]);

            // 提交事务
            Db::commit();

            return [
                'processed_count' => $processedCount
            ];

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            static::setError($e->getMessage());
            return false;
        }
    }
}
