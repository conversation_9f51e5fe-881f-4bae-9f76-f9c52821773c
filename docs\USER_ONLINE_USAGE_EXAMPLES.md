# 用户在线状态使用示例

## 🎯 基本使用

### 1. 检查单个用户是否在线

```php
// 方法一：使用服务类（推荐）
use app\common\service\UserOnlineService;
$isOnline = UserOnlineService::isUserOnline(1001);

// 方法二：使用便捷函数
$isOnline = check_user_online(1001);

if ($isOnline) {
    echo "用户在线";
} else {
    echo "用户离线";
}
```

### 2. 批量检查用户在线状态

```php
$userIds = [1001, 1002, 1003, 1004, 1005];

// 方法一：使用服务类
$onlineUsers = UserOnlineService::batchCheckOnlineStatus($userIds);

// 方法二：使用便捷函数
$onlineUsers = batch_check_users_online($userIds);

// 结果：[1001, 1003, 1005] (在线用户ID数组)
```

## 🚀 实际应用场景

### 1. 用户列表页面

```php
// 控制器中
public function getUserList()
{
    $users = User::field('user_id,nickname,avatar')->limit(20)->select();
    
    // 批量检查在线状态
    $userIds = array_column($users->toArray(), 'user_id');
    $onlineUsers = batch_check_users_online($userIds);
    
    // 添加在线状态标记
    foreach ($users as &$user) {
        $user['is_online'] = in_array($user['user_id'], $onlineUsers);
    }
    
    return json(['code' => 1, 'data' => $users]);
}
```

### 2. 动态列表（在线用户优先）

```php
public function getDynamicList()
{
    $dynamics = Dynamic::with(['user'])->limit(50)->select();
    
    // 批量检查发布者在线状态
    $userIds = array_column($dynamics->toArray(), 'user_id');
    $onlineUsers = batch_check_users_online($userIds);
    
    // 添加在线状态并排序
    foreach ($dynamics as &$dynamic) {
        $dynamic['user_online'] = in_array($dynamic['user_id'], $onlineUsers);
    }
    
    // 按在线状态排序：在线用户优先
    $dynamics = $dynamics->toArray();
    usort($dynamics, function($a, $b) {
        if ($a['user_online'] == $b['user_online']) {
            return $b['create_time'] - $a['create_time']; // 时间倒序
        }
        return $b['user_online'] - $a['user_online']; // 在线优先
    });
    
    return json(['code' => 1, 'data' => $dynamics]);
}
```

### 3. 视频通话发起

```php
public function startVideoCall()
{
    $userId = input('user_id');
    $targetUserId = input('target_user_id');
    
    // 检查对方是否在线
    if (!check_user_online($targetUserId)) {
        return json(['code' => 0, 'msg' => '对方当前不在线，无法发起通话']);
    }
    
    // 检查对方是否忙碌
    $targetUser = User::where('user_id', $targetUserId)->find();
    if ($targetUser['is_busy'] == 1) {
        return json(['code' => 0, 'msg' => '对方当前忙碌中']);
    }
    
    // 发起通话逻辑...
    return json(['code' => 1, 'msg' => '通话发起成功']);
}
```

### 4. 消息推送优化

```php
public function sendBroadcastMessage()
{
    $message = input('message');
    $targetUserIds = input('user_ids'); // [1001, 1002, 1003, ...]
    
    // 只向在线用户推送
    $onlineUsers = batch_check_users_online($targetUserIds);
    
    $successCount = 0;
    foreach ($onlineUsers as $userId) {
        // 发送推送消息
        $result = sendPushNotification($userId, $message);
        if ($result) {
            $successCount++;
        }
    }
    
    return json([
        'code' => 1,
        'msg' => "消息推送完成",
        'data' => [
            'total_users' => count($targetUserIds),
            'online_users' => count($onlineUsers),
            'success_count' => $successCount
        ]
    ]);
}
```

### 5. 礼物展示（全局广播）

```php
public function sendGift()
{
    $userId = input('user_id');
    $giftId = input('gift_id');
    $targetUserId = input('target_user_id');
    
    // 检查接收者是否在线
    if (!check_user_online($targetUserId)) {
        return json(['code' => 0, 'msg' => '对方已离线']);
    }
    
    // 发送礼物逻辑...
    
    // 如果是高价值礼物，全局广播
    $gift = Gift::find($giftId);
    if ($gift['price'] >= 1000) { // 价值1000钻石以上的礼物
        $message = [
            'type' => 'GLOBAL_GIFT',
            'data' => [
                'sender_id' => $userId,
                'receiver_id' => $targetUserId,
                'gift_id' => $giftId,
                'gift_name' => $gift['name']
            ]
        ];
        
        // 发送全局礼物消息
        send_global_gift_message($message);
    }
    
    return json(['code' => 1, 'msg' => '礼物发送成功']);
}
```

## 🔧 管理功能

### 1. 手动设置用户在线状态

```php
// 管理员功能：强制设置用户在线状态
public function setUserOnlineStatus()
{
    $userId = input('user_id');
    $isOnline = input('is_online', 1);
    $cacheTime = input('cache_time', 3600);
    
    // 设置用户在线状态
    set_user_online_status($userId, $isOnline, $cacheTime);
    
    return json(['code' => 1, 'msg' => '设置成功']);
}
```

### 2. 刷新用户在线缓存

```php
// 管理员功能：刷新指定用户的在线缓存
public function refreshUserCache()
{
    $userId = input('user_id');
    
    refresh_user_online_cache($userId);
    
    return json(['code' => 1, 'msg' => '缓存刷新成功']);
}
```

### 3. 在线用户统计

```php
public function getOnlineStats()
{
    // 获取所有用户ID
    $allUserIds = User::column('user_id');
    
    // 批量检查在线状态
    $onlineUsers = batch_check_users_online($allUserIds);
    
    // 按性别统计
    $maleOnline = 0;
    $femaleOnline = 0;
    
    $onlineUserDetails = User::whereIn('user_id', $onlineUsers)
        ->field('user_id,sex')
        ->select();
    
    foreach ($onlineUserDetails as $user) {
        if ($user['sex'] == 1) {
            $maleOnline++;
        } else {
            $femaleOnline++;
        }
    }
    
    return json([
        'code' => 1,
        'data' => [
            'total_users' => count($allUserIds),
            'online_users' => count($onlineUsers),
            'male_online' => $maleOnline,
            'female_online' => $femaleOnline,
            'online_rate' => round(count($onlineUsers) / count($allUserIds) * 100, 2) . '%'
        ]
    ]);
}
```

## 🕐 定时任务

### 1. 清理过期在线状态

```php
// 在定时任务中添加
public function cleanExpiredOnlineStatus()
{
    $cleanedCount = UserOnlineService::cleanExpiredOnlineStatus();
    
    echo "清理了 {$cleanedCount} 个过期的在线状态\n";
    
    // 记录日志
    LogService::write('定时清理过期在线状态', [
        'cleaned_count' => $cleanedCount,
        'execute_time' => date('Y-m-d H:i:s')
    ], 'cron_job');
}
```

### 2. 在线状态同步检查

```php
// 定期与IM服务器同步在线状态
public function syncOnlineStatusWithIM()
{
    // 获取数据库中标记为在线的用户
    $onlineUsers = User::where('is_online', 1)->column('user_id');
    
    $syncCount = 0;
    foreach ($onlineUsers as $userId) {
        // 通过IM服务器验证真实状态
        $realStatus = UserOnlineService::isUserOnline($userId);
        $syncCount++;
        
        // 避免频繁调用，每100个用户休息1秒
        if ($syncCount % 100 == 0) {
            sleep(1);
        }
    }
    
    echo "同步检查了 {$syncCount} 个用户的在线状态\n";
}
```

## 📊 性能优化建议

### 1. 缓存预热

```php
// 系统启动时预热热门用户的在线状态缓存
public function warmupOnlineCache()
{
    // 获取活跃用户（最近7天有登录的用户）
    $activeUsers = User::where('last_login_time', '>', time() - 7*24*3600)
        ->column('user_id');
    
    // 批量检查并缓存在线状态
    batch_check_users_online($activeUsers);
    
    echo "预热了 " . count($activeUsers) . " 个用户的在线状态缓存\n";
}
```

### 2. 分页批量处理

```php
// 对于大量用户的在线状态检查，使用分页处理
public function batchCheckLargeUserList($userIds)
{
    $pageSize = 100; // 每次处理100个用户
    $pages = array_chunk($userIds, $pageSize);
    $allOnlineUsers = [];
    
    foreach ($pages as $pageUsers) {
        $onlineUsers = batch_check_users_online($pageUsers);
        $allOnlineUsers = array_merge($allOnlineUsers, $onlineUsers);
        
        // 避免过于频繁的查询
        usleep(100000); // 休息0.1秒
    }
    
    return $allOnlineUsers;
}
```

这些示例展示了如何在实际项目中使用新的用户在线状态检查机制，既保证了准确性，又优化了性能！
