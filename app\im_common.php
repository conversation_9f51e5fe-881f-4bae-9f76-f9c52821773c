<?php
use app\common\service\tim\TencentImService;
use app\common\model\user\User;
use app\common\service\LogService;
use app\common\enum\ImMessageEnum;
use app\common\service\ConfigService;
use think\facade\Cache;
/**
 * IM服务单例实例
 * @var TencentImService|null
 */
$imServiceInstance = null;

/**
 * 获取IM服务实例（单例模式）
 * @return TencentImService
 */
function get_im_service_instance()
{
    global $imServiceInstance;
    if ($imServiceInstance === null) {
        $imServiceInstance = new TencentImService();
    }
    return $imServiceInstance;
}

/**
 * 获取IM配置
 * @param int $user_id 用户ID
 * @return array IM配置信息
 */
function get_im_config($user_id)
{
    try {
        $imService = get_im_service_instance();
        return $imService->getBaseConfig($user_id);
    } catch (\Exception $e) {
        LogService::write('获取IM配置失败', [
            'user_id' => $user_id,
            'error' => $e->getMessage()
        ], 'im_common');
        return [];
    }
}

/**
 * 创建IM用户
 * @param int $user_id 用户ID
 * @return array 创建结果
 */
function create_im_user_info($user_id)
{
    try {
        $userInfo = User::field('id,nickname,avatar')->find($user_id);
        if (!$userInfo) {
            return ['status' => false, 'message' => '用户不存在'];
        }

        $imService = get_im_service_instance();
        $imResult = $imService->createAccount(
            (string)$user_id,
            $userInfo['nickname'] ?? '',
            $userInfo['avatar'] ?? ''
        );

        return $imResult;
    } catch (\Exception $e) {
        LogService::write('创建IM用户失败', [
            'user_id' => $user_id,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '创建IM用户失败'];
    }
}

/**
 * 修改IM用户资料
 * @param int $user_id 用户ID
 * @return array 更新结果
 */
function update_im_user_info($user_id)
{
    try {
        $userInfo = User::field('id,nickname,avatar')->find($user_id);
        if (!$userInfo) {
            return ['status' => false, 'message' => '用户不存在'];
        }

        $imService = get_im_service_instance();
        $imResult = $imService->updateAccount(
            (string)$user_id,
            $userInfo['nickname'] ?? '',
            $userInfo['avatar'] ?? ''
        );

        return $imResult;
    } catch (\Exception $e) {
        LogService::write('更新IM用户资料失败', [
            'user_id' => $user_id,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '更新IM用户资料失败'];
    }
}

/**
 * 发送文本消息
 * @param int $user_id 发送者用户ID
 * @param int $to_user_id 接收者用户ID
 * @param string $msg 消息内容
 * @return array 发送结果
 */
function send_im_text_msg($user_id, $to_user_id, $msg)
{
    try {
        if (empty($msg)) {
            return ['status' => false, 'message' => '消息内容不能为空'];
        }

        // 构建消息结构
        $msgBody = [
            [
                'MsgType' => 'TIMTextElem',
                'MsgContent' => ['Text' => $msg]
            ]
        ];

        $imService = get_im_service_instance();
        $imResult = $imService->sendCustomMessage(
            (string)$user_id,
            (string)$to_user_id,
            $msgBody
        );

        return $imResult;
    } catch (\Exception $e) {
        LogService::write('发送IM文本消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'message' => $msg,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送消息失败'];
    }
}

/**
 * 发送视频通话挂断推送消息
 * @param int $user_id 发送者用户ID
 * @param int $to_user_id 接收者用户ID
 * @param array $video_call_info 视频通话信息（可选）
 * @return array 发送结果
 */
function end_video_call($user_id, $to_user_id, $video_call_info = [])
{
    try {
        // 获取发送者用户信息
        $senderInfo = User::field('id,nickname,avatar')->find($user_id);

        $ext = [
            'type' => 14, // 一对一视频消息挂断推送
            'channel' => 1, // 通话频道
            'reply_type' => 1,
            'sender' => [
                'id' => $user_id,
                'user_nickname' => $senderInfo['nickname'] ?? 'admin',
                'avatar' => $senderInfo['avatar'] ?? ''
            ]
        ];

        // 如果有额外的视频通话信息，合并到ext中
        if (!empty($video_call_info) && is_array($video_call_info)) {
            $ext = array_merge($ext, $video_call_info);
        }

        // 构建消息结构
        $msgBody = [
            [
                'MsgType' => 'TIMCustomElem', // 自定义类型
                'MsgContent' => [
                    'Data' => json_encode($ext),
                    'Desc' => '视频通话结束',
                ]
            ]
        ];

        $imService = get_im_service_instance();
        $imResult = $imService->sendCustomMessage(
            (string)$user_id,
            (string)$to_user_id,
            $msgBody
        );

        return $imResult;
    } catch (\Exception $e) {
        LogService::write('发送视频通话挂断消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'video_call_info' => $video_call_info,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送视频通话挂断消息失败'];
    }
}

/**
 * 发送自定义消息
 * @param int $user_id 发送者用户ID
 * @param int $to_user_id 接收者用户ID
 * @param int $type 消息类型
 * @param array $data 消息数据
 * @param string $desc 消息描述
 * @return array 发送结果
 */
function send_im_custom_msg($user_id, $to_user_id, $type, $data = [], $desc = '')
{
    try {
        $ext = [
            'type' => $type,
            'sender' => [
                'id' => $user_id
            ]
        ];

        // 合并自定义数据
        if (!empty($data) && is_array($data)) {
            $ext = array_merge($ext, $data);
        }

        // 构建消息结构
        $msgBody = [
            [
                'MsgType' => 'TIMCustomElem',
                'MsgContent' => [
                    'Data' => json_encode($ext),
                    'Desc' => $desc,
                ]
            ]
        ];

        $imService = get_im_service_instance();
        $imResult = $imService->sendCustomMessage(
            (string)$user_id,
            (string)$to_user_id,
            $msgBody
        );

        return $imResult;
    } catch (\Exception $e) {
        LogService::write('发送IM自定义消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'type' => $type,
            'data' => $data,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送自定义消息失败'];
    }
}

/**
 * 批量创建IM用户
 * @param array $user_ids 用户ID数组
 * @return array 批量创建结果
 */
function batch_create_im_users($user_ids)
{
    $results = [];
    $success_count = 0;
    $fail_count = 0;

    foreach ($user_ids as $user_id) {
        $result = create_im_user_info($user_id);
        $results[$user_id] = $result;

        if ($result['status']) {
            $success_count++;
        } else {
            $fail_count++;
        }
    }

    return [
        'total' => count($user_ids),
        'success' => $success_count,
        'fail' => $fail_count,
        'results' => $results
    ];
}

/**
 * 检查用户是否已创建IM账号
 * @param int $user_id 用户ID
 * @return bool 是否已创建
 */
function check_im_user_exists($user_id)
{
    try {
        $config = get_im_config($user_id);
        return !empty($config['usersig']);
    } catch (\Exception $e) {
        return false;
    }
}

/**
 * 发送图片消息
 * @param int $user_id 发送者用户ID
 * @param int $to_user_id 接收者用户ID
 * @param string $image_url 图片URL
 * @param int $width 图片宽度
 * @param int $height 图片高度
 * @return array 发送结果
 */
function send_im_image_msg($user_id, $to_user_id, $image_url, $width = 0, $height = 0)
{
    try {
        if (empty($image_url)) {
            return ['status' => false, 'message' => '图片URL不能为空'];
        }

        // 构建图片消息结构
        $msgBody = [
            [
                'MsgType' => 'TIMImageElem',
                'MsgContent' => [
                    'UUID' => uniqid(),
                    'ImageFormat' => pathinfo($image_url, PATHINFO_EXTENSION),
                    'ImageInfoArray' => [
                        [
                            'Type' => 1, // 原图
                            'Size' => 0,
                            'Width' => $width,
                            'Height' => $height,
                            'URL' => $image_url
                        ]
                    ]
                ]
            ]
        ];

        $imService = get_im_service_instance();
        $imResult = $imService->sendCustomMessage(
            (string)$user_id,
            (string)$to_user_id,
            $msgBody
        );

        return $imResult;
    } catch (\Exception $e) {
        LogService::write('发送IM图片消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'image_url' => $image_url,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送图片消息失败'];
    }
}

/**
 * 检查用户是否在线
 * @param int $user_id 用户ID
 * @return bool 是否在线
 */
function check_user_online($user_id)
{
    try {
        $cacheOnline = Cache::get("user_online_{$user_id}");
        if ($cacheOnline) {
            return true;
        }
        $imService = get_im_service_instance();
        $result = $imService->isUserOnline($user_id);
        if (isset($result['QueryResult'][0]['State'])) {
            $isOnline = ($result['QueryResult'][0]['State'] === 'Online');
            // 同步状态到本地
            if ($isOnline) {
                Cache::set("user_online_{$user_id}", time(), 3600);
                User::where('user_id', $user_id)->update(['is_online' => 1]);
            }
            return $isOnline;
        }
        return false;
    } catch (\Exception $e) {
        return false;
    }
}

/**
 * IM禁言用户
 * @param int $user_id 用户ID
 * @param int $time 禁言时间（秒）
 * @return array 禁言结果
 */
function im_shut_up($user_id, $time)
{
    try {
        $imService = get_im_service_instance();
        // 注意：这里需要在TencentImService中添加shut_up方法
        // 暂时返回模拟结果
        LogService::write('IM禁言用户', [
            'user_id' => $user_id,
            'time' => $time,
            'note' => '需要在TencentImService中实现shut_up方法'
        ], 'im_common');

        return ['status' => true, 'message' => '功能待实现'];
    } catch (\Exception $e) {
        LogService::write('IM禁言用户失败', [
            'user_id' => $user_id,
            'time' => $time,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => 'IM禁言失败'];
    }
}

/**
 * 失效账号登录状态（踢下线）
 * @param int $user_id 用户ID
 * @return array 踢下线结果
 */
function im_kick_account($user_id)
{
    try {
        $imService = get_im_service_instance();
        // 注意：这里需要在TencentImService中添加kick方法
        // 暂时返回模拟结果
        LogService::write('踢用户下线', [
            'user_id' => $user_id,
            'note' => '需要在TencentImService中实现kick方法'
        ], 'im_common');

        return ['status' => true, 'message' => '功能待实现'];
    } catch (\Exception $e) {
        LogService::write('踢用户下线失败', [
            'user_id' => $user_id,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '踢下线失败'];
    }
}

/**
 * 发送一对一视频警告推送
 * @param int $user_id 发送者用户ID
 * @param int $to_user_id 接收者用户ID
 * @param string $channel_id 频道ID
 * @param string $msg 警告消息
 * @return array 发送结果
 */
function winning_video_call($user_id, $to_user_id, $channel_id, $msg)
{
    try {
        $ext = [
            'type' => ImMessageEnum::ONE_ONE_WARNING_PUSH, // 114
            'channel' => $channel_id,
            'reply_type' => 1,
            'sender' => [
                'id' => $user_id,
                'user_nickname' => '',
                'avatar' => '',
                'msg' => $msg
            ]
        ];

        return send_im_custom_msg($user_id, $to_user_id, ImMessageEnum::ONE_ONE_WARNING_PUSH, $ext, '视频通话警告');
    } catch (\Exception $e) {
        LogService::write('发送视频通话警告失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'channel_id' => $channel_id,
            'message' => $msg,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送视频通话警告失败'];
    }
}

/**
 * 发送未接通挂断推送
 * @param int $user_id 发送者用户ID
 * @param int $to_user_id 接收者用户ID
 * @param array $video_call_info 视频通话信息
 * @return array 发送结果
 */
function huang_video_call($user_id, $to_user_id, $video_call_info)
{
    try {
        $ext = [
            'type' => ImMessageEnum::NOT_CONNECTED_DISCOMMECTED, // 13
            'channel' => $video_call_info['channel_id'] ?? '',
            'reply_type' => 2,
            'sender' => [
                'id' => $user_id,
                'user_nickname' => '',
                'avatar' => ''
            ]
        ];

        return send_im_custom_msg($user_id, $to_user_id, ImMessageEnum::NOT_CONNECTED_DISCOMMECTED, $ext, '未接通挂断');
    } catch (\Exception $e) {
        LogService::write('发送未接通挂断消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'video_call_info' => $video_call_info,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送未接通挂断消息失败'];
    }
}

/**
 * 发送通话结束消息（带通话时长）
 * @param int $user_id 发送者用户ID
 * @param int $to_user_id 接收者用户ID
 * @param string $channel_id 频道ID
 * @param int $call_type 通话类型（1=视频，2=语音）
 * @param int $call_time_length 通话时长（秒）
 * @return array 发送结果
 */
function im_push_call_end_msg($user_id, $to_user_id, $channel_id, $call_type, $call_time_length)
{
    try {
        // 格式化通话时长
        $minutes = floor($call_time_length / 60);
        $seconds = $call_time_length % 60;
        $duration_text = $minutes > 0 ? "{$minutes}分{$seconds}秒" : "{$seconds}秒";

        $ext = [
            'type' => ImMessageEnum::ONE_ONE_HANG_UP, // 14
            'channel' => $channel_id,
            'reply_type' => 1,
            'call_type' => $call_type,
            'msg_content' => "通话时长: {$duration_text}",
            'call_duration' => $call_time_length,
            'sender' => [
                'id' => $user_id,
                'user_nickname' => '',
                'avatar' => ''
            ]
        ];

        return send_im_custom_msg($user_id, $to_user_id, ImMessageEnum::ONE_ONE_HANG_UP, $ext, '通话结束');
    } catch (\Exception $e) {
        LogService::write('发送通话结束消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'channel_id' => $channel_id,
            'call_type' => $call_type,
            'call_time_length' => $call_time_length,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送通话结束消息失败'];
    }
}

/**
 * 发送管理员违规挂断消息
 * @param int $user_id 管理员用户ID
 * @param int $to_user_id 接收者用户ID
 * @param string $channel_id 频道ID
 * @param int $violation_type 违规类型
 * @param string $warning_msg 警告消息
 * @return array 发送结果
 */
function im_push_audit_violation_msg($user_id, $to_user_id, $channel_id, $violation_type, $warning_msg = '')
{
    try {
        $ext = [
            'type' => ImMessageEnum::ADMIN_ONE_ONE_HANG_UP, // 26
            'channelId' => $channel_id,
            'violation_type' => $violation_type,
            'warning_msg' => $warning_msg,
            'sender' => [
                'id' => $user_id,
                'user_nickname' => '',
                'avatar' => ''
            ]
        ];

        return send_im_custom_msg($user_id, $to_user_id, ImMessageEnum::ADMIN_ONE_ONE_HANG_UP, $ext, '管理员违规挂断');
    } catch (\Exception $e) {
        LogService::write('发送管理员违规挂断消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'channel_id' => $channel_id,
            'violation_type' => $violation_type,
            'warning_msg' => $warning_msg,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送管理员违规挂断消息失败'];
    }
}

/**
 * 发送拉黑删除IM聊天列表消息
 * @param int $block_uid 被拉黑的用户ID
 * @return array 发送结果
 */
function block_delete_im_list($block_uid)
{
    try {
        // 这里需要获取群组配置，暂时使用配置服务
        $group_id = ConfigService::get('im_config', 'acquire_group_id', '');
        $admin_id = ConfigService::get('im_config', 'tencent_identifier', 'admin');

        if (empty($group_id)) {
            return ['status' => false, 'message' => '群组配置未设置'];
        }

        $ext = [
            'type' => ImMessageEnum::BLOCK_DEL_IM_MESSAGE, // 779
            'block_uid' => $block_uid
        ];

        // 发送群组消息（这里需要实现群组消息发送功能）
        LogService::write('发送拉黑删除IM列表消息', [
            'block_uid' => $block_uid,
            'group_id' => $group_id,
            'admin_id' => $admin_id,
            'ext' => $ext,
            'note' => '需要实现群组消息发送功能'
        ], 'im_common');

        return ['status' => true, 'message' => '功能待实现'];
    } catch (\Exception $e) {
        LogService::write('发送拉黑删除IM列表消息失败', [
            'block_uid' => $block_uid,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送拉黑删除IM列表消息失败'];
    }
}


/**
 * 批量发送消息
 * @param int $from_user_id 发送者用户ID
 * @param array $to_user_ids 接收者用户ID数组
 * @param string $message 消息内容
 * @return array 批量发送结果
 */
function batch_send_im_text_msg($from_user_id, $to_user_ids, $message)
{
    try {
        if (empty($to_user_ids) || !is_array($to_user_ids)) {
            return ['status' => false, 'message' => '接收者用户ID数组不能为空'];
        }

        if (empty($message)) {
            return ['status' => false, 'message' => '消息内容不能为空'];
        }

        $results = [];
        $success_count = 0;
        $fail_count = 0;

        foreach ($to_user_ids as $to_user_id) {
            $result = send_im_text_msg($from_user_id, $to_user_id, $message);
            $results[$to_user_id] = $result;

            if ($result['status']) {
                $success_count++;
            } else {
                $fail_count++;
            }
        }

        return [
            'status' => $success_count > 0,
            'total' => count($to_user_ids),
            'success' => $success_count,
            'fail' => $fail_count,
            'results' => $results
        ];
    } catch (\Exception $e) {
        LogService::write('批量发送IM文本消息失败', [
            'from_user_id' => $from_user_id,
            'to_user_ids' => $to_user_ids,
            'message' => $message,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '批量发送消息失败'];
    }
}


/**
 * 发送匹配消息
 * @param int $user_id 用户ID
 * @param int $to_user_id 匹配的用户ID
 * @param array $match_info 匹配信息
 * @return array 发送结果
 */
function send_im_match_message($user_id, $to_user_id, $match_info = [])
{
    try {
        $ext = [
            'type' => ImMessageEnum::MATCH_MESSAGES, // 28
            'match_info' => $match_info,
            'sender' => [
                'id' => $user_id
            ]
        ];

        return send_im_custom_msg($user_id, $to_user_id, ImMessageEnum::MATCH_MESSAGES, $ext, '匹配消息');
    } catch (\Exception $e) {
        LogService::write('发送匹配消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'match_info' => $match_info,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送匹配消息失败'];
    }
}

/**
 * 发送搭讪消息
 * @param int $user_id 发送者用户ID
 * @param int $to_user_id 接收者用户ID
 * @param string $message 搭讪内容
 * @return array 发送结果
 */
function send_im_accost_message($user_id, $to_user_id, $message)
{
    try {
        $ext = [
            'type' => ImMessageEnum::ACCOST_MESSAGE, // 15
            'message' => $message,
            'sender' => [
                'id' => $user_id
            ]
        ];

        return send_im_custom_msg($user_id, $to_user_id, ImMessageEnum::ACCOST_MESSAGE, $ext, '搭讪消息');
    } catch (\Exception $e) {
        LogService::write('发送搭讪消息失败', [
            'user_id' => $user_id,
            'to_user_id' => $to_user_id,
            'message' => $message,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送搭讪消息失败'];
    }
}

/**
 * 发送全局礼物消息（全局广播飘屏）
 * @param array $send_user_info 发送者用户信息
 * @param array $to_user_info 接收者用户信息
 * @param int $count 礼物数量
 * @param string $gift_name 礼物名称
 * @param string $gift_icon 礼物图标
 * @param int $gift_coin 礼物价值（用于判断是否上电视）
 * @return array 发送结果
 */
function send_global_gift_message($send_user_info, $to_user_info, $count, $gift_name, $gift_icon, $gift_coin = 0)
{
    try {
        // 获取全局群组配置
        $group_id = ConfigService::get('tencent_im_config', 'tencent_group_id', '@FGC#FULLGROUPIDKEY');
        $admin_id = ConfigService::get('tencent_im_config', 'tencent_identifier', 'administrator');

        if (empty($group_id)) {
            return ['status' => false, 'message' => '全局群组配置未设置'];
        }

        // 构建全局礼物消息
        $broadMsg = [
            'type' => ImMessageEnum::GLOBAL_GIFT, // 777
            'channel' => 'all', // 全频道
            'sender' => [
                'id' => $send_user_info['id'],
                'user_nickname' => $send_user_info['user_nickname'] ?? $send_user_info['nickname'] ?? '',
                'avatar' => $send_user_info['avatar'] ?? ''
            ],
            'send_gift_info' => [
                'send_user_nickname' => $send_user_info['user_nickname'] ?? $send_user_info['nickname'] ?? '',
                'send_user_id' => $send_user_info['id'],
                'send_user_avatar' => $send_user_info['avatar'] ?? '',
                'send_to_user_id' => $to_user_info['id'],
                'send_to_user_nickname' => $to_user_info['user_nickname'] ?? $to_user_info['nickname'] ?? '',
                'send_to_user_avatar' => $to_user_info['avatar'] ?? '',
                'gift_icon' => $gift_icon,
                'gift_name' => $gift_name,
                'gift_count' => $count,
                'send_msg' => ($send_user_info['user_nickname'] ?? $send_user_info['nickname'] ?? '') .
                             ' 送给 ' . ($to_user_info['user_nickname'] ?? $to_user_info['nickname'] ?? '') .
                             ' ' . $count . '个' . $gift_name
            ],
            'on_tv_second' => 0 // 上电视展示秒数，0表示不上电视
        ];

        // 判断礼物是否上电视（可根据礼物价值判断）
        if ($gift_coin > 0) {
            // 这里可以根据礼物价值设置上电视时间
            // 例如：价值超过100的礼物上电视5秒
            if ($gift_coin * $count >= 100) {
                $broadMsg['on_tv_second'] = 5;
            }
        }

        // 发送群组消息
        $result = send_group_custom_message($admin_id, $group_id, ImMessageEnum::GLOBAL_GIFT, $broadMsg, '全局礼物消息');

        return $result;
    } catch (\Exception $e) {
        LogService::write('发送全局礼物消息失败', [
            'send_user_info' => $send_user_info,
            'to_user_info' => $to_user_info,
            'gift_name' => $gift_name,
            'gift_count' => $count,
            'gift_coin' => $gift_coin,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送全局礼物消息失败'];
    }
}

/**
 * 发送群组自定义消息
 * @param string $from_account 发送者账号
 * @param string $group_id 群组ID
 * @param int $msg_type 消息类型
 * @param array $ext_data 扩展数据
 * @param string $desc 消息描述
 * @return array 发送结果
 */
function send_group_custom_message($from_account, $group_id, $msg_type, $ext_data, $desc = '')
{
    try {
        $imService = get_im_service_instance();

        // 构建群组消息内容
        $msg_content = [
            [
                'MsgType' => 'TIMCustomElem',
                'MsgContent' => [
                    'Data' => json_encode($ext_data),
                    'Desc' => $desc
                ]
            ]
        ];

        // 调用TencentImService发送群组消息
        $result = $imService->sendGroupMessage($from_account, $group_id, $msg_content);

        if ($result && isset($result['ErrorCode']) && $result['ErrorCode'] === 0) {
            return ['status' => true, 'message' => '群组消息发送成功', 'data' => $result];
        } else {
            $error_msg = $result['ErrorInfo'] ?? '群组消息发送失败';
            return ['status' => false, 'message' => $error_msg, 'data' => $result];
        }
    } catch (\Exception $e) {
        LogService::write('发送群组自定义消息失败', [
            'from_account' => $from_account,
            'group_id' => $group_id,
            'msg_type' => $msg_type,
            'ext_data' => $ext_data,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '发送群组消息失败'];
    }
}

/**
 * 发送群组文本消息
 * @param string $from_account 发送者账号
 * @param string $group_id 群组ID
 * @param string $text_content 文本内容
 * @return array 发送结果
 */
function send_group_text_message($from_account, $group_id, $text_content)
{
    try {
        $imService = get_im_service_instance();

        // 构建群组文本消息内容
        $msg_content = [
            [
                'MsgType' => 'TIMTextElem',
                'MsgContent' => [
                    'Text' => $text_content
                ]
            ]
        ];

        // 调用TencentImService发送群组消息
        $result = $imService->sendGroupMessage($from_account, $group_id, $msg_content);

        if ($result && isset($result['ErrorCode']) && $result['ErrorCode'] === 0) {
            return ['status' => true, 'message' => '群组文本消息发送成功', 'data' => $result];
        } else {
            return ['status' => false, 'message' => '群组文本消息发送失败', 'error' => $result];
        }
    } catch (\Exception $e) {
        LogService::write('群组文本消息发送异常', [
            'from_account' => $from_account,
            'group_id' => $group_id,
            'text_content' => $text_content,
            'error' => $e->getMessage()
        ], 'im_common');

        return ['status' => false, 'message' => '群组文本消息发送异常: ' . $e->getMessage()];
    }
}

/**
 * 发送系统消息
 * @param string $user_id 用户ID
 * @param string $welcome_text 欢迎文本，默认为"你好"
 * @return array 发送结果
 */
function send_welcome_group_message($welcome_text = '你好')
{
    try {
        // 获取全局群组配置
        $group_id = ConfigService::get('tencent_im_config', 'tencent_group_id', '@FGC#FULLGROUPIDKEY');
        $admin_id = ConfigService::get('tencent_im_config', 'tencent_identifier', 'administrator');

        if (empty($group_id)) {
            return ['status' => false, 'message' => '全局群组配置未设置'];
        }

        // 发送文本消息
        $result = send_group_text_message($admin_id, $group_id, $welcome_text);

        return $result;
    } catch (\Exception $e) {
        LogService::write('发送群组消息失败', [
            'welcome_text' => $welcome_text,
            'error' => $e->getMessage()
        ], 'im_common');

        return ['status' => false, 'message' => '发送群组消息失败: ' . $e->getMessage()];
    }
}

/**
 * 创建全员广播大群
 * @param string $group_id 群组ID
 * @param string $group_name 群组名称
 * @param string $introduction 群组简介
 * @return array 创建结果
 */
function create_broadcast_group($group_id = '@FGC#FULLGROUPIDKEY', $group_name = 'FullGroup', $introduction = '全员广播群组')
{
    try {
        $imService = get_im_service_instance();
        $admin_id = ConfigService::get('tencent_im_config', 'tencent_identifier', 'administrator');

        // 创建BChatRoom类型的群组（全员广播大群）
        $result = $imService->createBroadcastGroup($group_id, $admin_id, $group_name, $introduction);

        if ($result && isset($result['ErrorCode']) && $result['ErrorCode'] === 0) {
            return ['status' => true, 'message' => '全员广播大群创建成功', 'data' => $result];

        } else {

            return ['status' => false, 'message' => '全员广播大群创建失败', 'error' => $result];
        }
    } catch (\Exception $e) {
        LogService::write('全员广播大群创建异常', [
            'group_id' => $group_id,
            'group_name' => $group_name,
            'error' => $e->getMessage()
        ], 'im_common');

        return ['status' => false, 'message' => '全员广播大群创建异常: ' . $e->getMessage()];
    }
}

/**
 * 创建音视频聊天室
 * @param string $group_id 群组ID (可选，不传则系统自动生成)
 * @param string $group_name 群组名称
 * @param string $introduction 群组简介
 * @return array 创建结果
 */
function create_av_chat_room($group_id = '', $group_name = 'AVChatRoom', $introduction = '音视频聊天室')
{
    try {
        $imService = get_im_service_instance();
        $admin_id = ConfigService::get('tencent_im_config', 'tencent_identifier', 'administrator');

        // 创建AVChatRoom类型的群组（音视频聊天室）
        $result = $imService->createAVChatRoom($group_id, $admin_id, $group_name, $introduction);

        if ($result && isset($result['ErrorCode']) && $result['ErrorCode'] === 0) {
            LogService::write('音视频聊天室创建成功', [
                'group_id' => $group_id,
                'group_name' => $group_name,
                'admin_id' => $admin_id,
                'result' => $result
            ], 'im_common');

            return ['status' => true, 'message' => '音视频聊天室创建成功', 'data' => $result];
        } else {
            LogService::write('音视频聊天室创建失败', [
                'group_id' => $group_id,
                'group_name' => $group_name,
                'admin_id' => $admin_id,
                'result' => $result
            ], 'im_common');

            return ['status' => false, 'message' => '音视频聊天室创建失败', 'error' => $result];
        }
    } catch (\Exception $e) {
        LogService::write('音视频聊天室创建异常', [
            'group_id' => $group_id,
            'group_name' => $group_name,
            'error' => $e->getMessage()
        ], 'im_common');

        return ['status' => false, 'message' => '音视频聊天室创建异常: ' . $e->getMessage()];
    }
}

/**
 * 销毁群组
 * @param string $group_id 群组ID
 * @return array 销毁结果
 */
function destroy_group($group_id)
{
    try {
        $imService = get_im_service_instance();

        $result = $imService->destroyGroup($group_id);

        if ($result && isset($result['ErrorCode']) && $result['ErrorCode'] === 0) {
            LogService::write('群组销毁成功', [
                'group_id' => $group_id,
                'result' => $result
            ], 'im_common');

            return ['status' => true, 'message' => '群组销毁成功', 'data' => $result];
        } else {
            LogService::write('群组销毁失败', [
                'group_id' => $group_id,
                'result' => $result
            ], 'im_common');

            return ['status' => false, 'message' => '群组销毁失败', 'error' => $result];
        }
    } catch (\Exception $e) {
        LogService::write('群组销毁异常', [
            'group_id' => $group_id,
            'error' => $e->getMessage()
        ], 'im_common');

        return ['status' => false, 'message' => '群组销毁异常: ' . $e->getMessage()];
    }
}

/**
 * 检查全局群组配置
 * @return array 检查结果
 */
function check_global_group_config()
{
    $group_id = ConfigService::get('tencent_im_config', 'tencent_group_id', '@FGC#FULLGROUPIDKEY');
    $admin_id = ConfigService::get('tencent_im_config', 'tencent_identifier', 'admin');

    if (empty($group_id)) {
        return [
            'status' => false,
            'message' => '全局群组ID未配置，请在腾讯云IM控制台创建群组后，将群组ID配置到 tencent_im_config.tencent_group_id 中'
        ];
    }

    if (empty($admin_id)) {
        return [
            'status' => false,
            'message' => '管理员账号未配置，请配置 tencent_im_config.tencent_identifier'
        ];
    }

    return [
        'status' => true,
        'message' => '全局群组配置正常',
        'group_id' => $group_id,
        'admin_id' => $admin_id
    ];
}

/**
 * 确保用户加入全局群组
 * @param int $user_id 用户ID
 * @return array 加入结果
 */
function ensure_user_join_global_group($user_id)
{
    try {
        $config_check = check_global_group_config();
        if (!$config_check['status']) {
            return $config_check;
        }

        $group_id = $config_check['group_id'];

        // 对于AVChatRoom类型的群组，通常由客户端SDK处理加入
        // 这里主要是记录日志和返回群组信息供客户端使用
        LogService::write('用户需要加入全局群组', [
            'user_id' => $user_id,
            'group_id' => $group_id,
            'note' => '请在客户端调用 tim.joinGroup() 加入群组'
        ], 'im_common');

        return [
            'status' => true,
            'message' => '请在客户端加入全局群组',
            'group_id' => $group_id,
            'group_type' => 'AVChatRoom',
            'client_action' => [
                'method' => 'joinGroup',
                'params' => [
                    'groupID' => $group_id,
                    'type' => 'TIM.TYPES.GRP_AVCHATROOM'
                ]
            ]
        ];
    } catch (\Exception $e) {
        LogService::write('检查用户全局群组状态失败', [
            'user_id' => $user_id,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '检查群组状态失败'];
    }
}

/**
 * 获取用户IM初始化配置（包含全局群组信息）
 * @param int $user_id 用户ID
 * @return array IM配置信息
 */
function get_user_im_init_config($user_id)
{
    try {
        $imService = get_im_service_instance();

        // 获取基础IM配置
        $base_config = $imService->getBaseConfig($user_id);

        // 获取全局群组配置
        $global_group_config = check_global_group_config();

        $config = [
            'status' => true,
            'user_config' => $base_config,
            'global_group' => $global_group_config['status'] ? [
                'group_id' => $global_group_config['group_id'],
                'group_type' => 'AVChatRoom',
                'auto_join' => true,
                'description' => '全局消息群组，用于接收礼物广播等全局消息'
            ] : null,
            'message' => 'IM配置获取成功'
        ];

        LogService::write('获取用户IM初始化配置', [
            'user_id' => $user_id,
            'config' => $config
        ], 'im_common');

        return $config;
    } catch (\Exception $e) {
        LogService::write('获取用户IM初始化配置失败', [
            'user_id' => $user_id,
            'error' => $e->getMessage()
        ], 'im_common');
        return ['status' => false, 'message' => '获取IM配置失败'];
    }
}



