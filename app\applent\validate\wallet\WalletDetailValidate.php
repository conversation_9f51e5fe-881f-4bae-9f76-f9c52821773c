<?php

namespace app\applent\validate\wallet;

use app\common\validate\BaseValidate;

/**
 * 钱包明细验证器
 * Class WalletDetailValidate
 * @package app\applent\validate\wallet
 */
class WalletDetailValidate extends BaseValidate
{
    protected $rule = [
        'date' => 'checkDateFormat',
        'pay_status' => 'integer|in:0,1,2',
    ];

    protected $message = [
        'date.checkDateFormat' => '日期格式错误',
    ];

    /**
     * @notes 支出明细列表场景
     * @return WalletDetailValidate
     */
    public function sceneExpenditureList()
    {
        return $this->only(['date']);
    }

    /**
     * @notes 收入明细列表场景
     * @return WalletDetailValidate
     */
    public function sceneIncomeList()
    {
        return $this->only(['date']);
    }

    /**
     * @notes 充值明细列表场景
     * @return WalletDetailValidate
     */
    public function sceneRechargeList()
    {
        return $this->only(['date', 'pay_status']);
    }

    /**
     * @notes 提现明细列表场景
     * @return WalletDetailValidate
     */
    public function sceneWithdrawalList()
    {
        return $this->only(['date']);
    }

    /**
     * @notes 自定义验证日期格式
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     */
    protected function checkDateFormat($value, $rule, $data)
    {
        if (empty($value)) {
            return true; // 允许为空
        }

        // 验证格式是否为 YYYY-MM
        if (!preg_match('/^\d{4}-\d{2}$/', $value)) {
            return '日期格式错误，请使用YYYY-MM格式';
        }

        // 验证日期是否有效
        $parts = explode('-', $value);
        $year = (int)$parts[0];
        $month = (int)$parts[1];

        if ($year < 2020 || $year > 2030) {
            return '年份必须在2020-2030之间';
        }

        if ($month < 1 || $month > 12) {
            return '月份必须在01-12之间';
        }

        return true;
    }
}
