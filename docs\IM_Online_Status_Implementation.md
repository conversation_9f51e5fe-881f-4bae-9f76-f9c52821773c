# IM用户在线状态查询功能实现文档

## 功能概述

实现了用户在线状态查询功能，采用缓存优先策略，提高查询性能并保持数据同步。

## 实现架构

### 1. 核心逻辑层 - LoginLogic

**文件位置**: `app/applent/logic/user/LoginLogic.php`

**核心方法**: `checkUserOnlineStatus($userId)`

**实现逻辑**:
1. 先查询缓存 (`user_online_status_{$user_id}`)
2. 缓存未命中时，调用腾讯云IM服务查询真实状态
3. 更新缓存（5分钟有效期）
4. 同步更新用户表的在线状态和最后在线时间

```php
public static function checkUserOnlineStatus($userId)
{
    try {
        // 1. 先查询缓存
        $cacheKey = "user_online_status_{$userId}";
        $cachedStatus = Cache::get($cacheKey);
        
        if ($cachedStatus !== null) {
            return (bool)$cachedStatus;
        }
        
        // 2. 缓存没有，查询IM服务器
        $imService = new TencentImService();
        $isOnline = $imService->isUserOnline($userId);
        
        // 3. 更新缓存（5分钟有效期）
        Cache::set($cacheKey, $isOnline, 300);
        
        // 4. 更新用户表的在线状态
        if ($isOnline) {
            User::where('id', $userId)->update([
                'is_online' => 1,
                'last_online_time' => time()
            ]);
        } else {
            User::where('id', $userId)->update(['is_online' => 0]);
        }
        
        return $isOnline;
        
    } catch (\Exception $e) {
        return false;
    }
}
```

### 2. 公共函数层 - im_common.php

**文件位置**: `app/im_common.php`

**核心函数**: `check_user_online($user_id)`

简化为调用LoginLogic中的方法：

```php
function check_user_online($user_id)
{
    // 调用LoginLogic中的方法
    return \app\applent\logic\user\LoginLogic::checkUserOnlineStatus($user_id);
}
```

### 3. API接口层 - VideoCallController

**文件位置**: `app/applent/controller/VideoCallController.php`

**接口方法**: `checkOnline()`

**请求参数**:
- `user_id`: 用户ID (必填)

**返回数据**:
```json
{
    "code": 1,
    "msg": "操作成功",
    "data": {
        "user_id": 123,
        "is_online": true,
        "online_status": "在线",
        "check_time": "2024-01-01 12:00:00"
    }
}
```

## 技术特点

### 1. 缓存策略
- **缓存键**: `user_online_status_{$user_id}`
- **有效期**: 5分钟
- **缓存优先**: 先查缓存，减少IM服务器请求

### 2. 数据同步
- **在线状态**: 同步更新 `la_user.is_online` 字段
- **最后在线时间**: 更新 `la_user.last_online_time` 字段
- **数据库字段**: 使用 `id` 作为主键字段（已修复）

### 3. 错误处理
- 异常时返回 `false`，避免影响业务流程
- 不抛出异常，保证系统稳定性

## 使用方法

### 1. 通过API接口调用

```bash
# GET请求
curl "http://your-domain/api/video_call/checkOnline?user_id=123"

# POST请求
curl -X POST "http://your-domain/api/video_call/checkOnline" \
     -d "user_id=123"
```

### 2. 在代码中调用

```php
// 方法1: 使用公共函数
$isOnline = check_user_online(123);

// 方法2: 直接调用LoginLogic
use app\applent\logic\user\LoginLogic;
$isOnline = LoginLogic::checkUserOnlineStatus(123);
```

## 配置要求

确保以下IM配置已正确设置：
- `im_sdk_app_id`: 腾讯云IM应用ID
- `im_identifier`: IM管理员账号
- `im_secret_key`: IM密钥

## 性能优化

1. **缓存机制**: 5分钟缓存减少IM服务器请求
2. **异步更新**: 数据库更新不影响返回速度
3. **错误容错**: 异常时快速返回，不阻塞业务

## 注意事项

1. **数据库字段**: 确保用户表使用正确的主键字段名
2. **缓存清理**: 用户登录/登出时可考虑清理相关缓存
3. **并发处理**: 高并发场景下可考虑加锁机制

## 测试建议

1. **功能测试**: 测试在线/离线状态查询
2. **缓存测试**: 验证缓存命中和过期机制
3. **性能测试**: 测试高并发查询性能
4. **异常测试**: 测试IM服务不可用时的处理

## 后续优化方向

1. **批量查询**: 支持批量查询多个用户状态
2. **实时推送**: 结合WebSocket推送状态变化
3. **统计分析**: 添加在线状态统计功能
4. **缓存预热**: 热点用户状态预加载
