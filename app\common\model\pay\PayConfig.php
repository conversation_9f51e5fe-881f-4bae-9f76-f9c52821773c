<?php

namespace app\common\model\pay;

use app\common\enum\PayEnum;
use app\common\model\BaseModel;
use app\common\service\FileService;


class PayConfig extends BaseModel
{
    /**
     * @notes 图标获取器 - 用于图标地址拼接域名
     * @param $value
     * @return string
     */
    public function getIconAttr($value)
    {
        return (!empty($value) && is_string($value)) ? FileService::getFileUrl(trim($value)) : '';
    }
}