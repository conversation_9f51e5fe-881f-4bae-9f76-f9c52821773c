<?php

namespace app\applent\logic;

use app\common\model\user\User;
use app\common\model\chatrecord\ChatRecordLog;
use app\common\model\onlineLog\OnlineLog;
use app\common\service\LogService;
use app\common\service\ConfigService;
use think\facade\Cache;

/**
 * IM回调业务逻辑类
 * 处理腾讯云IM回调事件的业务逻辑
 * 当前支持：用户状态变更回调、单聊消息回调
 * 注意：已移除签名验证功能
 * Class ImCallbackLogic
 * @package app\applent\logic
 */
class ImCallbackLogic
{
    /**
     * 处理用户状态变更回调
     * @param array $callbackData 回调数据
     * @return bool
     */
    public static function handleStateChange($callbackData)
    {
        try {
            $userId = $callbackData['Info']['To_Account'];
            $action = $callbackData['Info']['Action'];

            if (in_array($action, ['Logout', 'Disconnect', 'TimeOut'])) {
                // 用户下线处理
                return self::handleUserOffline($userId, $action);
            } elseif ($action == 'Login') {
                // 用户上线处理
                return self::handleUserOnline($userId, $callbackData);
            }

            return true;
        } catch (\Exception $e) {
            LogService::write('处理用户状态变更异常', [
                'user_id' => $userId ?? 0,
                'action' => $action ?? '',
                'error' => $e->getMessage()
            ], 'im_callback');
            return false;
        }
    }

    /**
     * 处理用户上线
     * @param int $userId 用户ID
     * @param array $callbackData 回调数据
     * @return bool
     */
    private static function handleUserOnline($userId, $callbackData)
    {
        try {
            // 更新用户在线状态
            $updateData = [
                'is_online' => 1,
                'last_online_time' => time(),
                'is_busy' => 0  // 上线时清除忙碌状态
            ];
            User::where('user_id', $userId)->update($updateData);

            // 创建在线记录
            OnlineLog::create([
                'user_id' => $userId,
                'online_time' => time(),
                'offline_time' => 0,
            ]);

            // 设置在线缓存标记
            Cache::set("user_online_{$userId}", time(), 3600);

            return true;
        } catch (\Exception $e) {
            LogService::write('处理用户上线异常', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ], 'im_callback');
            return false;
        }
    }

    /**
     * 处理用户下线
     * @param int $userId 用户ID
     * @param string $action 下线动作
     * @return bool
     */
    private static function handleUserOffline($userId, $action)
    {
        try {
            // 更新用户离线状态
            $updateData = [
                'is_online' => 0,
                'is_busy' => 0,
                'last_online_time' => time()
            ];
            User::where('user_id', $userId)->update($updateData);

            // 更新在线记录的离线时间
            $onlineLog = OnlineLog::where(['user_id' => $userId, 'offline_time' => 0])
                ->order('online_time desc')
                ->find();

            if ($onlineLog) {
                $duration = time() - $onlineLog['online_time'];
                OnlineLog::where('id', $onlineLog['id'])->update([
                    'offline_time' => time(),
                    'time' => $duration,
                ]);

                // 更新用户总在线时长
                User::where('user_id', $userId)
                    ->inc('online_time', $duration)
                    ->update();
            }

            // 清除在线缓存标记
            Cache::delete("user_online_{$userId}");

            // 处理视频通话中断
            self::handleVideoCallInterrupt($userId);

            return true;
        } catch (\Exception $e) {
            LogService::write('处理用户下线异常', [
                'user_id' => $userId,
                'action' => $action,
                'error' => $e->getMessage()
            ], 'im_callback');
            return false;
        }
    }

    /**
     * 处理消息发送前回调（消息过滤和审核）
     * @param array $callbackData 回调数据
     * @return array 处理结果
     */
    public static function handleBeforeSendMsg($callbackData)
    {
        try {
            $fromUserId = $callbackData['From_Account'];
            $toUserId = $callbackData['To_Account'];
            $msgBody = $callbackData['MsgBody'] ?? [];

            LogService::write('消息发送前回调', [
                'from_user' => $fromUserId,
                'to_user' => $toUserId,
                'msg_type' => $msgBody[0]['MsgType'] ?? 'unknown'
            ], 'im_callback');

            // 检查用户权限
            $permissionCheck = self::checkMessagePermission($fromUserId, $toUserId);
            if (!$permissionCheck['status']) {
                return [
                    'ActionStatus' => 'FAIL',
                    'ErrorCode' => 2,
                    'ErrorInfo' => $permissionCheck['message']
                ];
            }

            // 敏感词过滤
            $filterResult = self::filterSensitiveWords($msgBody);
            if ($filterResult['modified']) {
                return [
                    'ActionStatus' => 'OK',
                    'ErrorCode' => 0,
                    'ErrorInfo' => '',
                    'MsgBody' => $filterResult['msgBody']
                ];
            }

            return [
                'ActionStatus' => 'OK',
                'ErrorCode' => 0,
                'ErrorInfo' => ''
            ];

        } catch (\Exception $e) {
            LogService::write('消息发送前回调异常', [
                'from_user' => $fromUserId ?? 0,
                'to_user' => $toUserId ?? 0,
                'error' => $e->getMessage()
            ], 'im_callback');

            return [
                'ActionStatus' => 'FAIL',
                'ErrorCode' => 1,
                'ErrorInfo' => 'Server Error'
            ];
        }
    }

    /**
     * 处理消息发送后回调
     * @param array $callbackData 回调数据
     * @return bool
     */
    public static function handleAfterSendMsg($callbackData)
    {
        try {
            // 记录聊天日志
            self::addChatLog(
                $callbackData['From_Account'],
                $callbackData['To_Account'],
                $callbackData['MsgSeq'],
                $callbackData['MsgKey'],
                $callbackData['MsgTime'],
                $callbackData['MsgBody']
            );

            // 处理特殊消息类型（礼物、通话等）
            self::handleSpecialMessages($callbackData);

            // 推送通知（如果需要）
            self::handlePushNotification($callbackData);

            return true;
        } catch (\Exception $e) {
            LogService::write('消息发送后回调异常', [
                'from_user' => $callbackData['From_Account'] ?? 0,
                'to_user' => $callbackData['To_Account'] ?? 0,
                'error' => $e->getMessage()
            ], 'im_callback');
            return false;
        }
    }

    /**
     * 检查消息发送权限
     * @param int $fromUserId 发送者ID
     * @param int $toUserId 接收者ID
     * @return array
     */
    private static function checkMessagePermission($fromUserId, $toUserId)
    {
        // 获取系统管理员ID
        $adminId = ConfigService::get('im_config', 'tencent_identifier', 'admin');
        
        // 系统消息不检查权限
        if ($fromUserId == $adminId) {
            return ['status' => true, 'message' => ''];
        }

        // 检查发送者状态
        $fromUser = User::where('user_id', $fromUserId)->find();
        if (!$fromUser || $fromUser['delete_time'] > 0) {
            return ['status' => false, 'message' => '发送者账号不存在'];
        }

        if ($fromUser['ban_time'] > time()) {
            return ['status' => false, 'message' => '发送者账号被封禁'];
        }

        // 检查接收者状态
        $toUser = User::where('user_id', $toUserId)->find();
        if (!$toUser || $toUser['delete_time'] > 0) {
            return ['status' => false, 'message' => '接收者账号不存在'];
        }

        // 检查认证状态（如果需要）
        $requireAuth = ConfigService::get('im_config', 'require_auth_for_message', 0);
        if ($requireAuth && (!$fromUser['is_auth'] || !$toUser['is_auth'])) {
            return ['status' => false, 'message' => '需要完成实名认证才能发送消息'];
        }

        return ['status' => true, 'message' => ''];
    }

    /**
     * 敏感词过滤
     * @param array $msgBody 消息体
     * @return array
     */
    private static function filterSensitiveWords($msgBody)
    {
        $modified = false;
        
        if (isset($msgBody[0]['MsgType']) && $msgBody[0]['MsgType'] == 'TIMTextElem') {
            $text = $msgBody[0]['MsgContent']['Text'] ?? '';
            
            // 获取敏感词列表（从缓存或数据库）
            $sensitiveWords = Cache::remember('sensitive_words', function() {
                // 这里应该从数据库获取敏感词列表
                return ['测试敏感词', '违规内容']; // 示例
            }, 3600);

            $originalText = $text;
            foreach ($sensitiveWords as $word) {
                if (stripos($text, $word) !== false) {
                    $text = str_ireplace($word, str_repeat('*', mb_strlen($word, 'UTF-8')), $text);
                    $modified = true;
                }
            }

            if ($modified) {
                $msgBody[0]['MsgContent']['Text'] = $text;
                
                LogService::write('敏感词过滤', [
                    'original' => $originalText,
                    'filtered' => $text
                ], 'im_callback');
            }
        }

        return [
            'modified' => $modified,
            'msgBody' => $msgBody
        ];
    }

    /**
     * 添加聊天记录
     * @param int $uid 发送者ID
     * @param int $toUserId 接收者ID
     * @param int $msgSeq 消息序列号
     * @param string $msgKey 消息Key
     * @param int $msgTime 消息时间
     * @param array $msgBody 消息体
     */
    private static function addChatLog($uid, $toUserId, $msgSeq, $msgKey, $msgTime, $msgBody)
    {
        if (empty($uid) || empty($msgBody) || empty($msgBody[0]['MsgType'])) {
            return;
        }

        $userInfo = User::where('user_id', $uid)->field('user_nickname')->find();
        $toUserInfo = User::where('user_id', $toUserId)->field('user_nickname')->find();

        $type = 0;
        $content = '';
        $isAdd = true;

        switch ($msgBody[0]['MsgType']) {
            case 'TIMTextElem': 
                $type = 1; // 文本消息
                $content = $msgBody[0]['MsgContent']['Text'] ?? '';
                break;
            case 'TIMImageElem':
                $type = 2; // 图片消息
                $content = $msgBody[0]['MsgContent']['ImageInfoArray'][0]['URL'] ?? '';
                break;
            case 'TIMSoundElem':
                $type = 3; // 语音消息
                $content = $msgBody[0]['MsgContent']['Url'] ?? '';
                break;
            case 'TIMVideoFileElem':
                $type = 4; // 视频消息
                $content = $msgBody[0]['MsgContent']['VideoUrl'] ?? '';
                break;
            case 'TIMCustomElem':
                $type = 5; // 自定义消息
                $customData = json_decode($msgBody[0]['MsgContent']['Data'] ?? '', true);
                $content = $customData['desc'] ?? ($customData['content'] ?? 'Custom Message');
                if (empty($content) && empty($customData)) {
                    $isAdd = false;
                }
                break;
            default: 
                $isAdd = false;
        }

        if ($isAdd && !empty($content)) {
            ChatRecordLog::create([
                'user_id' => $uid,
                'to_user_id' => $toUserId,
                'user_nickname' => $userInfo['user_nickname'] ?? "User{$uid}",
                'to_user_nickname' => $toUserInfo['user_nickname'] ?? "User{$toUserId}",
                'type' => $type,
                'content' => $content,
                'create_time' => $msgTime,
                'format_time' => date('Y-m-d H:i:s', $msgTime),
                'msg_key' => $msgKey,
                'msg_seq' => $msgSeq
            ]);
        }
    }

    /**
     * 处理特殊消息类型
     * @param array $callbackData 回调数据
     */
    private static function handleSpecialMessages($callbackData)
    {
        $msgBody = $callbackData['MsgBody'] ?? [];
        
        if (isset($msgBody[0]['MsgType']) && $msgBody[0]['MsgType'] == 'TIMCustomElem') {
            $customData = json_decode($msgBody[0]['MsgContent']['Data'] ?? '', true);
            $messageType = $customData['type'] ?? 0;

            switch ($messageType) {
                case 5: // 礼物消息
                    self::handleGiftMessage($callbackData, $customData);
                    break;
                case 10: // 视频通话邀请
                case 11: // 视频通话接受
                case 12: // 视频通话拒绝
                case 13: // 视频通话结束
                    self::handleVideoCallMessage($callbackData, $customData);
                    break;
                case 777: // 全局礼物广播
                    self::handleGlobalGiftMessage($callbackData, $customData);
                    break;
            }
        }
    }

    /**
     * 处理礼物消息
     * @param array $callbackData 回调数据
     * @param array $customData 自定义数据
     */
    private static function handleGiftMessage($callbackData, $customData)
    {
        LogService::write('处理礼物消息', [
            'from_user' => $callbackData['From_Account'],
            'to_user' => $callbackData['To_Account'],
            'gift_data' => $customData
        ], 'im_callback');

        // 这里可以添加礼物相关的业务逻辑
        // 比如更新用户余额、礼物统计等
    }

    /**
     * 处理视频通话消息
     * @param array $callbackData 回调数据
     * @param array $customData 自定义数据
     */
    private static function handleVideoCallMessage($callbackData, $customData)
    {
        LogService::write('处理视频通话消息', [
            'from_user' => $callbackData['From_Account'],
            'to_user' => $callbackData['To_Account'],
            'call_data' => $customData
        ], 'im_callback');

        // 这里可以添加视频通话相关的业务逻辑
    }

    /**
     * 处理全局礼物广播消息
     * @param array $callbackData 回调数据
     * @param array $customData 自定义数据
     */
    private static function handleGlobalGiftMessage($callbackData, $customData)
    {
        LogService::write('处理全局礼物广播', [
            'from_user' => $callbackData['From_Account'],
            'gift_data' => $customData
        ], 'im_callback');

        // 这里可以添加全局礼物相关的业务逻辑
    }

    /**
     * 处理推送通知
     * @param array $callbackData 回调数据
     */
    private static function handlePushNotification($callbackData)
    {
        // 这里可以集成推送服务
        // 比如极光推送、友盟推送等
    }


    /**
     * 处理视频通话中断
     * @param int $userId 用户ID
     */
    private static function handleVideoCallInterrupt($userId)
    {
        // 这里可以添加视频通话中断的处理逻辑
        LogService::write('检查视频通话中断', [
            'user_id' => $userId
        ], 'im_callback');
    }


}
