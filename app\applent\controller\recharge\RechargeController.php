<?php

namespace app\applent\controller\recharge;

use app\applent\controller\BaseApiController;
use app\applent\logic\recharge\RechargeLogic;
use app\applent\validate\recharge\RechargeValidate;
use app\common\model\pay\PayConfig;
use app\common\service\LogService;
/**
 * 充值控制器
 * Class RechargeController
 * @package app\applent\controller\recharge
 */
class RechargeController extends BaseApiController
{
    public array $notNeedLogin = ['wechat_callback','alipay_callback','xiaoye_callback','wechat_return','alipay_return','xiaoye_return'];

    /**
     * @notes 获取充值套餐列表
     * @return \think\response\Json
     */
    public function get_package_list()
    {
        $result = RechargeLogic::getPackageList();
        if (false === $result) {
            return $this->fail(RechargeLogic::getError());
        }
        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 获取支付方式列表
     * @return \think\response\Json
     */
    public function get_pay_methods()
    {
        $result = RechargeLogic::getEnabledPayMethods();
        if (false === $result) {
            return $this->fail(RechargeLogic::getError());
        }
        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 创建充值订单并调起支付
     * @return \think\response\Json
     */
    public function create_order()
    {
        $params = (new RechargeValidate())->post()->goCheck('create_order');
        $result = RechargeLogic::createOrder($this->userId, $params);
        if (false === $result) {
            return $this->fail(RechargeLogic::getError());
        }
        return $this->success('订单创建并支付发起成功', $result, 1, 0);
    }

    /**
     * @notes 微信支付回调
     * @return string
     */
    public function wechat_callback()
    {
        try {
            // 微信回调数据是XML格式
            $xml = file_get_contents('php://input');
            if (!$xml) {
                return 'FAIL';
            }

            $data = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
            $params = json_decode(json_encode($data), true);

            if (empty($params)) {
                return 'FAIL';
            }

            // 处理支付回调
            $result = RechargeLogic::handlePayCallback($params, 'wechat_app');
            if ($result) {
                return '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
            } else {
                return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[' . RechargeLogic::getError() . ']]></return_msg></xml>';
            }

        } catch (\Exception $e) {
            return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[' . $e->getMessage() . ']]></return_msg></xml>';
        }
    }

    /**
     * @notes 支付宝支付回调
     * @return string
     */
    public function alipay_callback()
    {
        try {
            // 支付宝回调数据是POST表单格式
            $params = $_POST;

            if (empty($params)) {
                return 'fail';
            }

            // 处理支付回调
            $result = RechargeLogic::handlePayCallback($params, 'alipay_app');
            if ($result) {
                return 'success';
            } else {
                return 'fail';
            }

        } catch (\Exception $e) {
            return 'fail';
        }
    }

    /**
     * @notes 小叶支付回调
     * @return string
     */
    public function xiaoye_callback()
    {
        try {
            // 小叶支付回调数据可能是GET或POST格式
            $params = [];

            // 优先获取GET参数
            if (!empty($_GET)) {
                $params = $_GET;
            } elseif (!empty($_POST)) {
                $params = $_POST;
            }

            if (empty($params)) {
                // 记录日志
                LogService::write('支付回调数据为空', [
                    'raw' => $_GET
                ],'xiaoye_callback');
                return 'fail';
            }

            // 处理支付回调
            $result = RechargeLogic::handlePayCallback($params, 'xiaoye_pay');
            if ($result) {
                return 'success';
            } else {
                // 记录日志
                LogService::write('支付回调处理失败', [
                    'error' => RechargeLogic::getError(),
                    'params' => $params
                ],'xiaoye_callback');

                return 'fail';
            }

        } catch (\Exception $e) {
            // 记录日志
            LogService::write('回调异常', [
                'error' => $e->getMessage(),
                'params' => $params
            ],'xiaoye_callback');
            return 'fail';
        }
    }

    /**
     * @notes 微信支付返回页面
     * @return \think\response\Json
     */
    public function wechat_return()
    {
        return $this->success('支付完成，请返回应用查看结果');
    }

    /**
     * @notes 支付宝支付返回页面
     * @return \think\response\Json
     */
    public function alipay_return()
    {
        return $this->success('支付完成，请返回应用查看结果');
    }

    /**
     * @notes 小叶支付返回页面
     * @return \think\response\Json
     */
    public function xiaoye_return()
    {
        return $this->success('支付完成，请返回应用查看结果');
    }

    /**
     * @notes 查询订单支付状态
     * @return \think\response\Json
     */
    public function query_order_status()
    {
        $params = (new RechargeValidate())->goCheck('queryOrderStatus');
        $params['user_id'] = $this->userId;
        $result = RechargeLogic::queryOrderStatus($params);

        if ($result === false) {
            return $this->fail(RechargeLogic::getError());
        }
        return $this->success('查询成功', ['pay_status' => $result], 1, 0);
    }
}
