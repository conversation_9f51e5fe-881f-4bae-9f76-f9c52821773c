<?php

namespace app\common\enum\pay;

/**
 * 支付枚举类
 * Class PayEnum
 * @package app\common\enum\pay
 */
class PayEnum
{
    /**
     * 支付方式
     */
    const WECHAT_APP = 'wechat_app';        // 微信APP支付
    const ALIPAY_APP = 'alipay_app';        // 支付宝APP支付
    const XIAOYE_PAY = 'xiaoye_pay';        // 小叶支付

    /**
     * 支付状态
     */
    const STATUS_UNPAID = 0;                // 未支付
    const STATUS_PAID = 1;                  // 已支付
    const STATUS_REFUND = 2;                // 已退款
    const STATUS_CANCEL = 3;                // 已取消
    const STATUS_FAIL = 4;                  // 支付失败

    /**
     * 订单类型
     */
    const ORDER_TYPE_RECHARGE = 1;          // 充值订单
    const ORDER_TYPE_GIFT = 2;              // 礼物订单
    const ORDER_TYPE_VIP = 3;               // VIP订单

    /**
     * 获取支付方式名称
     * @param string $payWay
     * @return string
     */
    public static function getPayWayDesc($payWay)
    {
        $desc = [
            self::WECHAT_APP => '微信APP支付',
            self::ALIPAY_APP => '支付宝APP支付',
            self::XIAOYE_PAY => '小叶支付',
        ];
        return $desc[$payWay] ?? '未知支付方式';
    }

    /**
     * 获取支付状态名称
     * @param int $status
     * @return string
     */
    public static function getStatusDesc($status)
    {
        $desc = [
            self::STATUS_UNPAID => '未支付',
            self::STATUS_PAID => '已支付',
            self::STATUS_REFUND => '已退款',
            self::STATUS_CANCEL => '已取消',
            self::STATUS_FAIL => '支付失败',
        ];
        return $desc[$status] ?? '未知状态';
    }

    /**
     * 获取订单类型名称
     * @param int $orderType
     * @return string
     */
    public static function getOrderTypeDesc($orderType)
    {
        $desc = [
            self::ORDER_TYPE_RECHARGE => '充值订单',
            self::ORDER_TYPE_GIFT => '礼物订单',
            self::ORDER_TYPE_VIP => 'VIP订单',
        ];
        return $desc[$orderType] ?? '未知类型';
    }

    /**
     * 获取所有支付方式
     * @return array
     */
    public static function getPayWayOptions()
    {
        return [
            self::WECHAT_APP => self::getPayWayDesc(self::WECHAT_APP),
            self::ALIPAY_APP => self::getPayWayDesc(self::ALIPAY_APP),
            self::XIAOYE_PAY => self::getPayWayDesc(self::XIAOYE_PAY),
        ];
    }
}
