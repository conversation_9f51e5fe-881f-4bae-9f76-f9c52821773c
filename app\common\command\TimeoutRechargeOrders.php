<?php
declare(strict_types=1);

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\applent\logic\crontab\CrontabLogic;

/**
 * 处理超时充值订单命令
 * 基于现有Crontab.php文件的结构和风格
 *
 * 使用方法：
 * php think timeout:recharge
 */
class TimeoutRechargeOrders extends Command
{
    protected function configure()
    {
        $this->setName('timeout:recharge')
             ->setDescription('处理超时充值订单');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('<info>开始处理超时充值订单...</info>');

        // 记录开始时间，与现有Crontab.php风格保持一致
        $startTime = microtime(true);

        try {
            // 直接调用逻辑层方法，与现有Crontab.php风格保持一致
            $result = CrontabLogic::handleTimeoutRechargeOrders();

            if ($result === false) {
                $error = CrontabLogic::getError();
                $output->writeln('<error>处理失败: ' . $error . '</error>');
                return 1;
            }

            // 处理成功，输出结果
            if (isset($result['processed_count'])) {
                if ($result['processed_count'] == 0) {
                    $output->writeln('<comment>没有需要处理的超时订单</comment>');
                } else {
                    $output->writeln("<info>处理完成，共处理 {$result['processed_count']} 个超时订单</info>");

                    // 输出详细信息（如果有）
                    if (isset($result['details']) && !empty($result['details'])) {
                        $output->writeln('<comment>处理详情:</comment>');
                        foreach ($result['details'] as $detail) {
                            $output->writeln("  - 订单ID: {$detail['order_id']}, 状态: {$detail['status']}");
                        }
                    }
                }
            }

            $output->writeln('<info>任务执行完成</info>');
            return 0;

        } catch (\Exception $e) {
            $output->writeln('<error>执行异常: ' . $e->getMessage() . '</error>');
            return 1;
        }
    }
}