<?php

namespace app\applent\controller\wallet;

use app\applent\controller\BaseApiController;
use app\applent\lists\wallet\ExpenditureLists;
use app\applent\lists\wallet\IncomeLists;
use app\applent\lists\wallet\RechargeLists;
use app\applent\lists\wallet\WithdrawalLists;
use app\applent\validate\wallet\WalletDetailValidate;

/**
 * 钱包明细控制器
 * Class WalletDetailController
 * @package app\applent\controller\wallet
 */
class WalletDetailController extends BaseApiController
{
    /**
     * @notes 支出明细列表
     * @return \think\response\Json
     */
    public function get_expenditure_list()
    {
        $params = (new WalletDetailValidate())->goCheck('expenditureList');
        return $this->dataLists(new ExpenditureLists());
    }

    /**
     * @notes 收入明细列表
     * @return \think\response\Json
     */
    public function get_income_list()
    {
        $params = (new WalletDetailValidate())->goCheck('incomeList');
        return $this->dataLists(new IncomeLists());
    }

    /**
     * @notes 获取充值明细列表
     * @return \think\response\Json
     */
    public function get_recharge_list()
    {
        $params = (new WalletDetailValidate())->goCheck('rechargeList');
        return $this->dataLists(new RechargeLists());
    }

    /**
     * @notes 获取提现明细列表
     * @return \think\response\Json
     */
    public function get_withdrawal_list()
    {
        $params = (new WalletDetailValidate())->goCheck('withdrawalList');
        return $this->dataLists(new WithdrawalLists());
    }
}
