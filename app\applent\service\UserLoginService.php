<?php

namespace app\applent\service;

use app\common\model\user\User;
use app\common\model\user\UserSession;
use think\facade\Request;
use app\common\service\tim\TencentImService;
use app\applent\service\UserTokenService;
use think\facade\Config;
use app\common\service\FileService;
use app\common\model\user\UserInviteCode;
use app\common\model\user\UserInviteRecord;
use app\common\service\ConfigService;
use app\common\model\user\UserDataReview;
use think\facade\Db;

class UserLoginService
{
    // 热门省市对应关系
    private static $hotProvinceCities = [
        '北京市' => ['东城区', '西城区', '朝阳区', '海淀区', '丰台区', '石景山区'],
        '上海市' => ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区'],
        '广东省' => ['广州市', '深圳市', '珠海市', '佛山市', '东莞市', '中山市'],
        '浙江省' => ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市'],
        '江苏省' => ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市'],
        '四川省' => ['成都市', '自贡市', '攀枝花市', '泸州市', '德阳市', '绵阳市'],
        '湖北省' => ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市'],
        '陕西省' => ['西安市', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市'],
        '重庆市' => ['万州区', '涪陵区', '渝中区', '大渡口区', '江北区', '沙坪坝区'],
        '天津市' => ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区']
    ];

    /**
     * 随机生成省市信息
     * @return array 返回包含省份和城市的数组
     */
    private static function getRandomProvinceCity()
    {
        // 随机选择一个省份
        $provinces = array_keys(self::$hotProvinceCities);
        $randomProvince = $provinces[array_rand($provinces)];

        // 从该省份中随机选择一个城市
        $cities = self::$hotProvinceCities[$randomProvince];
        $randomCity = $cities[array_rand($cities)];

        return [
            'province' => $randomProvince,
            'city' => $randomCity
        ];
    }

    /**
     * 手机号登录/密码登录/注册统一入口
     * @param array $params 包含 mobile/terminal/ip 等登录参数
     * @return array 返回登录凭证及用户信息
     */
    public static function findOrCreateUser($params)
    {
        $user = User::where('mobile', $params['mobile'])->where(['delete_time'=>null])->findOrEmpty();

         // 密码处理逻辑
        if (isset($params['password'])) {
            $passwordSalt = Config::get('project.unique_identification');
            $inputPassword = create_password($params['password'], $passwordSalt);
            
            if($user->isEmpty()){
                throw new \Exception('账号不存在');
            }
            if($user->password !== $inputPassword){
                throw new \Exception('密码错误');
            }
            // 注册或更新时存储加密后的密码
            $params['password'] = $inputPassword;
        }

        if ($user->isEmpty()) {
            //新用户注册流程：创建用户记录并初始化基础信息
            $user->id = self::createUser($params);
        }else{
            if($user->ban_time > time()){
                throw new \Exception('账号已被封禁,'.date('Y-m-d H:i:s', $user->ban_time).'解封');
            }
            if($user->is_disable == 1){
                throw new \Exception('账号已被禁用');
            }
            //已存在用户：更新登录IP和时间等数据
            self::updateLoginInfo($user->id, $params);
        }

        // 获取IM基础配置
        $imConfig = get_im_config($user->id);

        //生成用户访问令牌
        $userInfo = UserTokenService::setToken($user->id, $params['terminal']);
        return [
            'user_info' => $userInfo,
            'im_config' => $imConfig,
        ];
    }

    /**
     * 创建新用户
     * @param array $params 包含 mobile/terminal/ip 等注册参数
     * @return int 返回新创建用户的ID
     */
    public static function createUser($params)
    {
        // 开启事务
        Db::startTrans();
        try {
            $userSn = User::createUserSn();
            $user = User::create([
                'sn' => $userSn,
                'nickname' => '用户' . $userSn,
                'account' => 'u' . $userSn,
                'mobile' => $params['mobile'],
                'channel' => $params['terminal'],
                'password'=>isset($params['password'])?$params['password']:'',
                'login_ip' => $params['ip'],
                'register_ip' => $params['ip'],
                'login_time' => time(),
                'create_time' => time(),
                'device_name' => $params['device_name'],
                'device_id'  => $params['device_id'],
                'is_disable' =>0,
            ]);

            //同步创建IM用户
            $imResult = create_im_user_info($user->id);

            // 提交事务
            Db::commit();
            return $user->id;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 处理邀请码逻辑
     * @param int $userId 被邀请人ID
     * @param string $inviterCode 邀请码/邀请人
     * @return void
     */
    private static function handleInviteCode($userId, $inviterCode)
    {

        // 检查是否已存在邀请记录（防止重复邀请）
        $existingRecord = UserInviteRecord::where('invite_user_id', $userId)->findOrEmpty();
        if (!$existingRecord->isEmpty()) {
            return; // 已存在邀请记录，直接返回
        }

        // 创建邀请记录
        UserInviteRecord::create([
            'user_id' => $inviterCode,           // 邀请人ID
            'invite_user_id' => $userId,       // 被邀请人ID
            'create_time' => time(),           // 邀请时间
        ]);
    }

    /**
     * 更新用户登录信息
     * 在用户重复登录时更新关键登录元数据
     * @param int $userId 用户ID
     * @param string $ip 当前登录IP地址
     */
    public static function updateLoginInfo($userId, $params)
    {
        User::where('id', $userId)->update([
            'login_ip' => $params['ip'],
            'password'=>isset($params['password'])?$params['password']:'',
            'channel'=>$params['terminal'],
            'login_time' => time(),
            'device_name' => $params['device_name'],
            'device_id'  => $params['device_id'],
        ]);
    }

     /**
     * 用户信息完善
     * @param int $userId 待更新用户ID
     * @param array $params 包含 nickname/avatar/sex/birthday 等用户资料
     * @return string 返回更新后的用户令牌
     * IM同步：通过腾讯云IM服务更新用户昵称和头像，保持通讯资料一致性
     */
    public static function updateUserInfo($userId, $params)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 获取用户信息
            $user = User::find($userId);

            $age = calculateAge($params['birthday']);
            //大于18才可以
            if($age < 18){
                throw new \Exception('您的年龄未满18岁，无法注册');
            }

             // 根据性别设置默认头像
            if ($params['sex'] == 1) {    // 男性
                $avatar = ConfigService::get('systemconfig', 'default_man_avatar');
                $thumb_avatar = ConfigService::get('systemconfig', 'default_man_avatar');
            } else {                    // 女性
                $avatar = ConfigService::get('systemconfig', 'default_woman_avatar');
                $thumb_avatar = ConfigService::get('systemconfig', 'default_woman_avatar');
            }

            // 处理头像审核
            if ($params['avatar']) {
                // 检查是否需要审核头像
                $isAvatarExamine = ConfigService::get('systemconfig', 'is_avatar_examine', 0);

                if ($isAvatarExamine == 1) {
                    // 需要审核，插入审核表
                    $avatarUrl = FileService::setFileUrl($params['avatar']);

                    $thumb_avatar_url = FileService::setFileUrl($params['thumb_avatar']);
                    // 创建审核记录
                    $reviewData = [
                        'user_id' => $userId, // 用户ID
                        'image' => $avatarUrl,
                        'thumb_avatar' => $thumb_avatar_url,
                        'type' => 1, // 1=头像
                        'status' => 0, // 0=待审核
                        'create_time' => time(),
                        'update_time' => time()
                    ];

                    // 插入审核表
                    UserDataReview::create($reviewData);

                } else {
                    // 不需要审核，直接使用上传的头像
                    $avatar = FileService::setFileUrl($params['avatar']);
                    $thumb_avatar = FileService::setFileUrl($params['thumb_avatar']);
                }
            }

            // 随机生成省市信息
            $provinceCity = self::getRandomProvinceCity();

            //修改用户资料
            User::update([
                'id'            =>$userId,
                'nickname'      => $params['nickname'],
                'avatar'        => $avatar,
                'thumb_avatar'  => $thumb_avatar,
                'sex'           => $params['sex'] ?? 0,
                'birthday'      => $params['birthday'],
                'is_reg_perfect'=>1,
                'login_ip'      =>request()->ip(),
                'province'      => $provinceCity['province'],
                'city'          => $provinceCity['city'],
                'login_time'    =>time(),
                'update_time'   =>time(),
                'age'           =>$age,
                'constellation' =>get_constellation($params['birthday']),
                'video_price'   => ConfigService::get('systemconfig', 'min_video_price', 100),
                'voice_price'   => ConfigService::get('systemconfig', 'min_voice_price', 50),
            ]);

            // 处理邀请码逻辑
            if (isset($params['inviter_code']) && !empty($params['inviter_code'])) {
                self::handleInviteCode($userId, $params['inviter_code']);
            }

            // 更新IM用户资料
            $imResult = update_im_user_info($userId);
            $token = UserSession::where('user_id',$userId)->value('token');

            // 提交事务
            Db::commit();
            return $token;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }
}
