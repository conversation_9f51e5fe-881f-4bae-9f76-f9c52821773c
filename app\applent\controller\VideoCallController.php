<?php

namespace app\applent\controller;

use app\applent\validate\gift\GiftSendValidate;
use app\common\service\ConfigService;
use app\common\model\gift\Gift;
use app\common\model\gift\GiftRecord;
use app\common\model\user\User;
use app\common\model\user\UserBalance;
use app\common\model\user\UserCoinLog;
use app\common\model\user\UserIncomeLog;
use app\common\model\user\UserRebateRatio;
use app\common\model\user\UserInviteRecord;
use think\facade\Db;
use think\facade\Cache;

class VideoCallController extends BaseApiController
{
    /**
     * @notes 赠送礼物接口
     * @return \think\response\Json
     */
    public function sendGift()
    {
        // 参数验证
        $params = (new GiftSendValidate())->goCheck('sendGift');

        // 防重复提交检查（1分钟内不能重复赠送相同礼物给同一用户）
        $cacheKey = "gift_send_{$this->userId}_{$params['to_user_id']}_{$params['gift_id']}";
        if (Cache::get($cacheKey)) {
            return $this->fail('操作过于频繁，请稍后再试');
        }

        Db::startTrans();
        try {
            // 1. 验证用户不能给自己送礼物
            if ($this->userId == $params['to_user_id']) {
                return $this->fail('不能给自己赠送礼物');
            }

            // 2. 获取礼物信息
            $gift = Gift::find($params['gift_id']);
            if (!$gift) {
                return $this->fail('礼物不存在');
            }

            // 3. 获取接收用户信息
            $toUser = User::find($params['to_user_id']);
            if (!$toUser) {
                return $this->fail('接收用户不存在');
            }

            // 4. 计算总金额
            $totalAmount = $gift['coin'] * $params['gift_count'];

            // 5. 检查用户余额
            $userBalance = UserBalance::getUserBalance($this->userId);
            if (!$userBalance) {
                $userBalance = UserBalance::createUserBalance($this->userId);
            }

            if ($userBalance['balance'] < $totalAmount) {
                return $this->fail('余额不足，请先充值');
            }

            // 6. 计算抽成比例和实际收益
            $commissionData = $this->calculateCommission($toUser, $totalAmount);
            $commissionRate = $commissionData['rate'];
            $actualIncome = $commissionData['income'];

            // 7. 扣除送礼用户余额
            $beforeBalance = $userBalance['balance'];
            $afterBalance = $beforeBalance - $totalAmount;
            UserBalance::where('user_id', $this->userId)
                ->update([
                    'balance' => $afterBalance,
                    'update_time' => time()
                ]);

            // 8. 记录送礼用户余额变动
            $orderNo = 'GIFT_' . date('YmdHis') . mt_rand(1000, 9999);
            UserCoinLog::addGiftLog(
                $this->userId,
                $beforeBalance,
                $afterBalance,
                -$totalAmount,
                $orderNo,
                $params['to_user_id']
            );

            // 9. 增加接收用户收益
            $toUserBalance = UserBalance::getUserBalance($params['to_user_id']);
            if (!$toUserBalance) {
                $toUserBalance = UserBalance::createUserBalance($params['to_user_id']);
            }

            $beforeIncome = $toUserBalance['income'];
            $afterIncome = $beforeIncome + $actualIncome;
            UserBalance::where('user_id', $params['to_user_id'])
                ->update([
                    'income' => $afterIncome,
                    'update_time' => time()
                ]);

            // 10. 记录接收用户收益变动
            UserIncomeLog::addGiftLog(
                $params['to_user_id'],
                $beforeIncome,
                $afterIncome,
                $actualIncome,
                $orderNo,
                $this->userId,
                '', // 昵称自动获取
                $commissionRate,
                $totalAmount
            );

            // 11. 添加礼物记录
            $giftRecordData = [
                'user_id' => $this->userId,
                'to_user_id' => $params['to_user_id'],
                'to_nickname' => $toUser['nickname'],
                'gift_id' => $params['gift_id'],
                'gift_name' => $gift['name'],
                'gift_count' => $params['gift_count'],
                'gift_price' => $gift['coin'],
                'total_amount' => $totalAmount,
                'commission_rate' => $commissionRate,
                'actual_income' => $actualIncome,
                'source_type' => $params['source_type'],
                'source_id' => $params['source_id'] ?? '',
                'is_global' => $gift['is_all_notify'] ?? 0,
            ];

            $giftRecord = GiftRecord::addRecord($giftRecordData);
            if (!$giftRecord) {
                throw new \Exception('礼物记录创建失败');
            }

            // 12. 处理上级返佣逻辑
            $this->handleRebateCommission($params['to_user_id'], $totalAmount, $orderNo);

            // 13. 判断是否需要全局广播
            if ($gift['is_all_notify'] == 1) {
                $this->sendGlobalGiftMessage($this->userInfo, $toUser, $params['gift_count'], $gift['name'], $gift['img'], $gift['coin']);
            }

            Db::commit();

            // 设置防重复提交缓存（1分钟）
            Cache::set($cacheKey, time(), 60);

            return $this->success('礼物赠送成功', [
                'order_no' => $orderNo,
                'gift_name' => $gift['name'],
                'gift_count' => $params['gift_count'],
                'total_amount' => $totalAmount,
                'remaining_balance' => $afterBalance,
                'is_global' => $gift['is_all_notify'] ?? 0
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return $this->fail('礼物赠送失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 计算抽成比例和实际收益
     * @param User $toUser 接收用户
     * @param float $totalAmount 礼物总金额
     * @return array
     */
    private function calculateCommission($toUser, $totalAmount)
    {
        // 检查用户是否开启个人抽成设置
        if ($toUser['is_open_gift_commission'] == 1) {
            // 从用户返佣比例表获取抽成比例
            $userRebate = UserRebateRatio::getUserRebateRatio($toUser['id']);
            if ($userRebate && isset($userRebate['gift_commission'])) {
                $commissionRate = floatval($userRebate['gift_commission']);
            } else {
                // 如果没有设置，使用系统默认配置
                $commissionRate = floatval(ConfigService::get('systemconfig', 'gift_commission', 30));
            }
        } else {
            // 使用系统配置的抽成比例
            $commissionRate = floatval(ConfigService::get('systemconfig', 'gift_commission', 30));
        }

        // 计算实际收益 = 总金额 * (100 - 抽成比例) / 100
        $actualIncome = $totalAmount * (100 - $commissionRate) / 100;

        return [
            'rate' => $commissionRate,
            'income' => $actualIncome
        ];
    }

    /**
     * @notes 处理上级返佣逻辑
     * @param int $toUserId 接收礼物用户ID
     * @param float $totalAmount 礼物总金额
     * @param string $orderNo 订单号
     */
    private function handleRebateCommission($toUserId, $totalAmount, $orderNo)
    {
        try {
            // 查找一级邀请人
            $level1Record = UserInviteRecord::where('invite_user_id', $toUserId)->find();
            if (!$level1Record) {
                return; // 没有邀请人，直接返回
            }

            // 获取一级邀请人信息
            $level1User = User::find($level1Record->user_id);
            if (!$level1User) {
                return;
            }

            // 处理一级返佣
            if ($level1User->is_one_rake_back == 1) {
                $level1Rate = $this->getUserRebateRate($level1User, 2, 1); // 类型2=礼物，级别1=一级
                if ($level1Rate > 0) {
                    $level1Amount = $totalAmount * $level1Rate / 100;

                    // 增加一级用户收益
                    $parentBalance = UserBalance::getUserBalance($level1User->id);
                    if (!$parentBalance) {
                        $parentBalance = UserBalance::createUserBalance($level1User->id);
                    }

                    $beforeIncome = $parentBalance['income'];
                    $afterIncome = $beforeIncome + $level1Amount;

                    UserBalance::where('user_id', $level1User->id)
                        ->update([
                            'income' => $afterIncome,
                            'update_time' => time()
                        ]);

                    // 记录一级返佣日志
                    UserIncomeLog::addGiftCommissionLog(
                        $level1User->id,
                        $beforeIncome,
                        $afterIncome,
                        $level1Amount,
                        1, // 一级返佣
                        $orderNo,
                        $this->userId,
                        $level1Rate,
                        $totalAmount
                    );
                }
            }

            // 检查是否开启二级返佣
            $enableLevel2Rebate = ConfigService::get('systemconfig', 'enable_two_rebate', 0);
            if ($enableLevel2Rebate) {
                // 查找二级邀请人
                $level2Record = UserInviteRecord::where('invite_user_id', $level1Record->user_id)->find();
                if ($level2Record) {
                    // 获取二级用户信息
                    $level2User = User::find($level2Record->user_id);
                    if ($level2User && $level2User->is_two_rake_back == 1) {
                        $level2Rate = $this->getUserRebateRate($level2User, 2, 2); // 类型2=礼物，级别2=二级
                        if ($level2Rate > 0) {
                            $level2Amount = $totalAmount * $level2Rate / 100;

                            // 增加二级用户收益
                            $grandParentBalance = UserBalance::getUserBalance($level2User->id);
                            if (!$grandParentBalance) {
                                $grandParentBalance = UserBalance::createUserBalance($level2User->id);
                            }

                            $beforeIncome = $grandParentBalance['income'];
                            $afterIncome = $beforeIncome + $level2Amount;

                            UserBalance::where('user_id', $level2User->id)
                                ->update([
                                    'income' => $afterIncome,
                                    'update_time' => time()
                                ]);

                            // 记录二级返佣日志
                            UserIncomeLog::addGiftCommissionLog(
                                $level2User->id,
                                $beforeIncome,
                                $afterIncome,
                                $level2Amount,
                                2, // 二级返佣
                                $orderNo,
                                $this->userId,
                                $level2Rate,
                                $totalAmount
                            );
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // 返佣失败不影响主流程，只记录日志
            \think\facade\Log::error('礼物返佣处理失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 获取用户返佣比例
     * @param object $user 用户对象
     * @param int $type 类型 1=充值 2=礼物 3=音频
     * @param int $level 级别 1=一级 2=二级
     * @return float 返佣比例（百分比）
     */
    private function getUserRebateRate($user, $type, $level)
    {
        try {
            // 检查用户是否开启对应级别返佣
            if ($level == 1 && $user->is_one_rake_back == 1) {
                // 开启，从user_rebate_ratio表获取返佣比例
                $rebateRatio = UserRebateRatio::where('user_id', $user->id)->find();

                if ($rebateRatio) {
                    // 根据类型和级别获取对应的返佣比例字段
                    $fieldName = $this->getRebateRatioFieldName($type, $level);
                    return floatval($rebateRatio[$fieldName] ?? 0);
                } else {
                    return 0;
                }
            }

            // 检查用户是否开启二级返佣
            if ($level == 2 && $user->is_two_rake_back == 1) {
                // 开启，从user_rebate_ratio表获取返佣比例
                $rebateRatio = UserRebateRatio::where('user_id', $user->id)->find();

                if ($rebateRatio) {
                    // 根据类型和级别获取对应的返佣比例字段
                    $fieldName = $this->getRebateRatioFieldName($type, $level);
                    return floatval($rebateRatio[$fieldName] ?? 0);
                } else {
                    return 0;
                }
            }

            // 非特殊用户或没有个性化配置，使用系统配置
            $configName = $this->getRebateRatioFieldName($type, $level);
            return floatval(ConfigService::get('systemconfig', $configName, 0));

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * @notes 获取返佣比例字段名
     * @param int $type 类型 1=充值 2=礼物 3=音频
     * @param int $level 级别 1=一级 2=二级
     * @return string
     */
    private function getRebateRatioFieldName($type, $level)
    {
        $typeMap = [
            1 => 'recharge', // 充值
            2 => 'gift',     // 礼物
            3 => 'audio'     // 音频
        ];

        $levelMap = [
            1 => 'one',  // 一级
            2 => 'two'   // 二级
        ];

        $typeName = $typeMap[$type] ?? 'gift';
        $levelName = $levelMap[$level] ?? 'one';

        return "{$typeName}_{$levelName}_rebate_rate";
    }

    /**
     * @notes 发送全局礼物广播消息
     * @param array $senderInfo 发送者信息
     * @param User $toUser 接收者信息
     * @param int $giftCount 礼物数量
     * @param string $giftName 礼物名称
     * @param string $giftIcon 礼物图标
     * @param float $giftCoin 礼物价值
     */
    private function sendGlobalGiftMessage($senderInfo, $toUser, $giftCount, $giftName, $giftIcon, $giftCoin)
    {
        try {
            // 调用全局礼物广播函数
            send_global_gift_message(
                [
                    'id' => $senderInfo['id'],
                    'nickname' => $senderInfo['nickname'],
                    'avatar' => $senderInfo['avatar']
                ],
                [
                    'id' => $toUser['id'],
                    'nickname' => $toUser['nickname'],
                    'avatar' => $toUser['avatar']
                ],
                $giftCount,
                $giftName,
                $giftIcon,
                $giftCoin
            );
        } catch (\Exception $e) {
            // 全局广播失败不影响主流程，只记录日志
            \think\facade\Log::error('全局礼物广播失败：' . $e->getMessage());
        }
    }
}
