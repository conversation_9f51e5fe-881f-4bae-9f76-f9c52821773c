# 礼物赠送接口文档

## 接口概述

本接口实现了完整的礼物赠送功能，包括余额扣除、收益分配、抽成计算、返佣处理和全局广播等功能。

## 接口地址

```
POST /applent/video_call/sendGift
```

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| gift_id | int | 是 | 礼物ID，必须大于0 |
| gift_count | int | 是 | 礼物数量，范围1-999 |
| source_type | int | 是 | 来源类型：1=查看个人资料，2=视频通话，3=消息页面，4=直播间 |
| to_user_id | int | 是 | 接收用户ID，必须大于0 |
| source_id | string | 否 | 来源ID，最大长度50字符 |

## 请求示例

```json
{
    "gift_id": 1,
    "gift_count": 5,
    "source_type": 2,
    "to_user_id": 123,
    "source_id": "video_call_456"
}
```

## 响应参数

### 成功响应

```json
{
    "code": 1,
    "msg": "礼物赠送成功",
    "data": {
        "order_no": "GIFT_20241201123456789",
        "gift_name": "玫瑰花",
        "gift_count": 5,
        "total_amount": 50.00,
        "remaining_balance": 450.00,
        "is_global": 1
    }
}
```

### 错误响应

```json
{
    "code": 0,
    "msg": "余额不足，请先充值",
    "data": []
}
```

## 业务逻辑

### 1. 参数验证
- 验证礼物ID、数量、来源类型、接收用户ID等参数
- 防重复提交检查（1分钟内不能重复赠送相同礼物给同一用户）
- 验证用户不能给自己送礼物

### 2. 余额处理
- 检查送礼用户余额是否充足
- 扣除送礼用户余额
- 记录余额变动日志

### 3. 收益计算
- 根据接收用户的抽成设置计算实际收益
- 支持个人抽成设置和系统默认抽成
- 增加接收用户收益
- 记录收益变动日志

### 4. 返佣处理
- 查找邀请关系链
- 处理一级返佣（如果开启）
- 处理二级返佣（如果开启）
- 记录返佣日志

### 5. 礼物记录
- 创建完整的礼物赠送记录
- 包含所有相关信息：用户、礼物、金额、抽成等

### 6. 全局广播
- 对于高价值礼物进行全局广播
- 调用IM系统发送广播消息

## 数据库表

### 1. gift_records（礼物记录表）
存储所有礼物赠送记录，包含详细的交易信息。

### 2. user_balance（用户余额表）
管理用户余额和收益。

### 3. user_coin_log（余额变动日志）
记录所有余额变动。

### 4. user_income_log（收益变动日志）
记录所有收益变动，包含抽成信息。

### 5. user_rebate_ratio（用户返佣比例表）
存储用户个性化返佣设置。

### 6. user_invite_record（用户邀请记录表）
管理用户邀请关系链。

## 配置项

### 系统配置
- `gift_commission`: 默认礼物抽成比例
- `enable_two_rebate`: 是否开启二级返佣
- 各种返佣比例配置

### 用户配置
- `is_open_gift_commission`: 是否开启个人抽成设置
- `is_one_rake_back`: 是否开启一级返佣
- `is_two_rake_back`: 是否开启二级返佣

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 操作失败 |
| 1 | 操作成功 |

## 常见错误信息

- "操作过于频繁，请稍后再试" - 防重复提交限制
- "不能给自己赠送礼物" - 业务逻辑限制
- "礼物不存在" - 礼物ID无效
- "接收用户不存在" - 用户ID无效
- "余额不足，请先充值" - 余额不够
- "礼物赠送失败：xxx" - 系统异常

## 注意事项

1. 接口使用事务处理，确保数据一致性
2. 返佣失败不影响主流程
3. 全局广播失败不影响主流程
4. 所有金额计算保留2位小数
5. 支持防重复提交机制
6. 完整的日志记录便于追踪

## 测试建议

1. 测试各种参数验证场景
2. 测试余额不足情况
3. 测试返佣逻辑
4. 测试全局广播功能
5. 测试防重复提交
6. 测试异常回滚机制
