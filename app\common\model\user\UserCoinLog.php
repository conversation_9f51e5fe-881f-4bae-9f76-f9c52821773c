<?php

namespace app\common\model\user;

use app\common\model\BaseModel;

/**
 * 用户金币变动日志模型
 * Class UserCoinLog
 * @package app\common\model\user
 */
class UserCoinLog extends BaseModel
{
    protected $name = 'user_balance_log';

    // 变动类型常量
    const TYPE_ADD = 1; // 增加
    const TYPE_SUB = 2; // 减少

    // 变动来源常量
    const SOURCE_RECHARGE = 1; // 充值
    const SOURCE_GIFT = 2;     // 礼物赠送
    const SOURCE_CALL = 3;     // 语音视频通话

    /**
     * @notes 记录金币变动日志
     * @param int $userId 用户ID
     * @param int $beforeCoin 变动前金币
     * @param int $afterCoin 变动后金币
     * @param int $changeCoin 变动金币（正数增加，负数减少）
     * @param int $source 来源：1=充值,2=礼物赠送,3=语音视频通话
     * @param int $type 类型：1=增加,2=减少
     * @param string $orderNo 订单号
     * @param int $toUserId 去向用户ID（充值时为0）
     * @return bool
     */
    public static function addLog($userId, $beforeCoin, $afterCoin, $changeCoin, $source, $type, $orderNo, $toUserId = 0, $toNickname = '')
    {
        // 如果没有传递昵称且有来源用户ID，自动获取昵称
        if (empty($toNickname) && $toUserId > 0) {
            $toUser = User::where('id', $toUserId)->find();
            $toNickname = $toUser ? $toUser->nickname : '';
        }

        $data = [
            'user_id' => $userId,
            'before_coin' => $beforeCoin,
            'after_coin' => $afterCoin,
            'change_coin' => $changeCoin,
            'source' => $source,
            'type' => $type,
            'source_no' => $orderNo,
            'to_user_id' => $toUserId,
            'to_nickname' => $toNickname,
            'create_time' => time(),
            'update_time' => time(),
        ];

        return self::create($data) ? true : false;
    }

    /**
     * @notes 记录充值金币日志
     * @param int $userId 用户ID
     * @param int $beforeCoin 充值前金币数量
     * @param int $afterCoin 充值后金币数量
     * @param int $changeCoin 充值金币数量
     * @param string $orderNo 充值订单号
     * @return bool
     */
    public static function addRechargeLog($userId, $beforeCoin, $afterCoin, $changeCoin, $orderNo)
    {
        return self::addLog(
            $userId,
            $beforeCoin,
            $afterCoin,
            $changeCoin,
            self::SOURCE_RECHARGE,
            self::TYPE_ADD,
            $orderNo,
            0 // 充值时to_user_id为0
        );
    }

    /**
     * @notes 记录礼物赠送金币日志
     * @param int $userId 用户ID
     * @param int $beforeCoin 变动前金币数量
     * @param int $afterCoin 变动后金币数量
     * @param int $changeCoin 变动金币数量（正数增加，负数减少）
     * @param string $orderNo 礼物订单号
     * @param int $toUserId 接收礼物的用户ID
     * @return bool
     */
    public static function addGiftLog($userId, $beforeCoin, $afterCoin, $changeCoin, $orderNo, $toUserId)
    {
        $type = $changeCoin > 0 ? self::TYPE_ADD : self::TYPE_SUB;

        return self::addLog(
            $userId,
            $beforeCoin,
            $afterCoin,
            $changeCoin,
            self::SOURCE_GIFT,
            $type,
            $orderNo,
            $toUserId
        );
    }

    /**
     * @notes 记录语音视频通话金币日志
     * @param int $userId 用户ID
     * @param int $beforeCoin 变动前金币数量
     * @param int $afterCoin 变动后金币数量
     * @param int $changeCoin 变动金币数量（正数增加，负数减少）
     * @param string $orderNo 通话订单号
     * @param int $toUserId 通话对方用户ID
     * @return bool
     */
    public static function addCallLog($userId, $beforeCoin, $afterCoin, $changeCoin, $orderNo, $toUserId)
    {
        $type = $changeCoin > 0 ? self::TYPE_ADD : self::TYPE_SUB;

        return self::addLog(
            $userId,
            $beforeCoin,
            $afterCoin,
            $changeCoin,
            self::SOURCE_CALL,
            $type,
            $orderNo,
            $toUserId
        );
    }

    /**
     * @notes 获取用户金币变动日志
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param int $source 来源筛选（可选）
     * @return array
     */
    public static function getUserCoinLogs($userId, $page = 1, $limit = 20, $source = 0)
    {
        $where = ['user_id' => $userId];
        if ($source > 0) {
            $where['source'] = $source;
        }

        $list = self::where($where)
            ->field('type,change_coin,before_coin,after_coin,source,source_no,to_user_id,create_time')
            ->order('create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        $total = self::where($where)->count();

        // 处理数据
        foreach ($list as &$item) {
            $item['type_text'] = $item['type'] == self::TYPE_ADD ? '增加' : '减少';
            $item['source_text'] = self::getSourceText($item['source']);
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
        }

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit),
        ];
    }

    /**
     * @notes 获取来源文本
     * @param int $source 来源
     * @return string
     */
    private static function getSourceText($source)
    {
        $sourceTexts = [
            self::SOURCE_RECHARGE => '充值',
            self::SOURCE_GIFT => '礼物赠送',
            self::SOURCE_CALL => '语音视频通话',
        ];

        return $sourceTexts[$source] ?? '未知';
    }

    /**
     * @notes 按月份搜索器
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchDateAttr($query, $value, $data)
    {
        if (!empty($value)) {
            // 解析 YYYY-MM 格式
            $parts = explode('-', $value);
            if (count($parts) == 2) {
                $year = (int)$parts[0];
                $month = (int)$parts[1];

                // 计算该月的开始和结束时间
                $startTime = strtotime($year . '-' . sprintf('%02d', $month) . '-01 00:00:00');
                $endTime = strtotime(date('Y-m-t 23:59:59', $startTime)); // 该月最后一天

                $query->where('create_time', 'between', [$startTime, $endTime]);
            }
        } else {
            // 如果没有传入日期，默认查找最近一周的数据
            $startTime = strtotime('-7 days');
            $endTime = time();
            $query->where('create_time', 'between', [$startTime, $endTime]);
        }
    }
}
