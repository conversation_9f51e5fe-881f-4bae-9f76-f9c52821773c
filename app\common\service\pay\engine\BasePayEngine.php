<?php

namespace app\common\service\pay\engine;

/**
 * 支付引擎基类
 * Class BasePayEngine
 * @package app\common\service\pay\engine
 */
abstract class BasePayEngine
{
    /**
     * 错误信息
     * @var string|null
     */
    protected $error = null;

    /**
     * 支付配置
     * @var array
     */
    protected $config = [];

    /**
     * 订单信息
     * @var array
     */
    protected $orderInfo = [];

    /**
     * 构造函数
     * @param array $config 支付配置
     */
    public function __construct($config = [])
    {
        if (empty($config)) {
            $this->error = '请联系管理员配置支付参数';
            return;
        }
        $this->config = $config;
        $this->initialize();
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initialize()
    {
        // 子类可重写此方法进行初始化
    }

    /**
     * 设置订单信息
     * @param array $orderInfo 订单信息
     * @return $this
     */
    public function setOrderInfo($orderInfo)
    {
        $this->orderInfo = $orderInfo;
        return $this;
    }

    /**
     * 获取错误信息
     * @return string|null
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * 设置错误信息
     * @param string $error 错误信息
     * @return void
     */
    protected function setError($error)
    {
        $this->error = $error;
    }

    /**
     * 是否有错误
     * @return bool
     */
    public function hasError()
    {
        return !empty($this->error);
    }

    /**
     * 统一下单
     * @param array $params 下单参数
     * @return array|false
     */
    abstract public function unifiedOrder($params);

    /**
     * 查询订单
     * @param string $orderNo 订单号
     * @return array|false
     */
    abstract public function queryOrder($orderNo);

    /**
     * 处理支付回调
     * @param array $params 回调参数
     * @return array|false
     */
    abstract public function handleNotify($params);

    /**
     * 验证回调签名
     * @param array $params 回调参数
     * @return bool
     */
    abstract public function verifyNotify($params);

    /**
     * 关闭订单
     * @param string $orderNo 订单号
     * @return array|false
     */
    abstract public function closeOrder($orderNo);

    /**
     * 申请退款
     * @param array $params 退款参数
     * @return array|false
     */
    abstract public function refund($params);

    /**
     * 查询退款
     * @param string $refundNo 退款单号
     * @return array|false
     */
    abstract public function queryRefund($refundNo);

    /**
     * 获取支付引擎名称
     * @return string
     */
    abstract public function getEngineName();

    /**
     * 生成随机字符串
     * @param int $length 长度
     * @return string
     */
    protected function generateNonceStr($length = 32)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $str = '';
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    /**
     * 记录日志
     * @param string $message 日志信息
     * @param array $context 上下文
     * @return void
     */
    protected function log($message, $context = [])
    {
        \think\facade\Log::info('[' . $this->getEngineName() . '] ' . $message, $context);
    }
}
