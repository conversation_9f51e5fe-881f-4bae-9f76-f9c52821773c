# IM在线状态查询接口测试指南

## 🎯 概述

已为您创建了完整的腾讯云IM用户在线状态查询功能，包括：
- **TencentImService类**：核心IM服务方法
- **im_common.php**：便捷函数
- **ImOnlineTestController**：测试控制器

## 📋 测试接口列表

### 1. 单个用户在线状态查询
**接口地址**: `/applent/im_online_test/testSingleUser`
**请求方式**: GET/POST
**参数**:
- `user_id` (可选): 用户ID，默认1001

**示例请求**:
```
GET /applent/im_online_test/testSingleUser?user_id=1001
```

**响应示例**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "user_id": 1001,
        "method1_result": true,
        "method2_full_result": {
            "ActionStatus": "OK",
            "ErrorCode": 0,
            "QueryResult": [
                {
                    "To_Account": "1001",
                    "State": "Online"
                }
            ]
        },
        "method3_result": true,
        "query_time": "2024-01-15 14:30:25"
    }
}
```

### 2. 批量用户在线状态查询
**接口地址**: `/applent/im_online_test/testBatchUsers`
**请求方式**: GET/POST
**参数**:
- `user_ids` (可选): 用户ID数组，默认[1001,1002,1003,1004,1005]

**示例请求**:
```
GET /applent/im_online_test/testBatchUsers?user_ids=1001,1002,1003
POST /applent/im_online_test/testBatchUsers
{
    "user_ids": [1001, 1002, 1003, 1004, 1005]
}
```

**响应示例**:
```json
{
    "code": 1,
    "msg": "批量查询成功",
    "data": {
        "total_users": 5,
        "online_users": ["1001", "1003", "1005"],
        "online_count": 3,
        "detail_results": [
            {"user_id": 1001, "is_online": true},
            {"user_id": 1002, "is_online": false},
            {"user_id": 1003, "is_online": true},
            {"user_id": 1004, "is_online": false},
            {"user_id": 1005, "is_online": true}
        ],
        "query_time_ms": 156.78,
        "query_time": "2024-01-15 14:30:25"
    }
}
```

### 3. 用户列表带在线状态
**接口地址**: `/applent/im_online_test/testUserList`
**请求方式**: GET/POST
**参数**:
- `limit` (可选): 查询用户数量，默认10

**示例请求**:
```
GET /applent/im_online_test/testUserList?limit=5
```

**响应示例**:
```json
{
    "code": 1,
    "msg": "用户列表查询成功",
    "data": {
        "users": [
            {
                "user_id": 1001,
                "nickname": "张三",
                "avatar": "avatar1.jpg",
                "create_time": "2024-01-01 10:00:00",
                "is_online": true,
                "online_status": "在线"
            },
            {
                "user_id": 1002,
                "nickname": "李四",
                "avatar": "avatar2.jpg", 
                "create_time": "2024-01-02 11:00:00",
                "is_online": false,
                "online_status": "离线"
            }
        ],
        "total_users": 5,
        "online_count": 2,
        "query_time_ms": 89.45,
        "query_time": "2024-01-15 14:30:25"
    }
}
```

### 4. 带缓存的在线状态查询
**接口地址**: `/applent/im_online_test/testWithCache`
**请求方式**: GET/POST
**参数**:
- `user_id` (可选): 用户ID，默认1001
- `force_refresh` (可选): 是否强制刷新缓存，0=否，1=是

**示例请求**:
```
GET /applent/im_online_test/testWithCache?user_id=1001&force_refresh=0
```

**缓存命中响应**:
```json
{
    "code": 1,
    "msg": "缓存命中",
    "data": {
        "user_id": 1001,
        "is_online": true,
        "from_cache": true,
        "query_time": "2024-01-15 14:30:25"
    }
}
```

**IM服务器查询响应**:
```json
{
    "code": 1,
    "msg": "IM服务器查询成功",
    "data": {
        "user_id": 1001,
        "is_online": true,
        "from_cache": false,
        "query_time_ms": 234.56,
        "cached_until": "2024-01-15 14:35:25",
        "query_time": "2024-01-15 14:30:25"
    }
}
```

### 5. 在线状态统计
**接口地址**: `/applent/im_online_test/testOnlineStats`
**请求方式**: GET/POST

**示例请求**:
```
GET /applent/im_online_test/testOnlineStats
```

**响应示例**:
```json
{
    "code": 1,
    "msg": "在线统计查询成功",
    "data": {
        "total_users": 100,
        "online_users": 25,
        "offline_users": 75,
        "male_online": 15,
        "female_online": 10,
        "online_rate": "25.00%",
        "online_user_details": [
            {
                "user_id": 1001,
                "nickname": "张三",
                "sex": 1
            }
        ],
        "query_time_ms": 456.78,
        "query_time": "2024-01-15 14:30:25"
    }
}
```

### 6. 清除测试缓存
**接口地址**: `/applent/im_online_test/clearTestCache`
**请求方式**: GET/POST
**参数**:
- `user_id` (必需): 要清除缓存的用户ID

**示例请求**:
```
GET /applent/im_online_test/clearTestCache?user_id=1001
```

## 🔧 代码使用示例

### 1. 在业务代码中使用

```php
// 检查单个用户是否在线
$isOnline = check_user_online(1001);

// 批量检查用户在线状态
$onlineUsers = batch_check_users_online([1001, 1002, 1003]);

// 获取完整的查询结果
$fullResult = im_check_user_online_state(1001);
```

### 2. 在控制器中使用

```php
class VideoCallController extends BaseApiController
{
    public function startCall()
    {
        $targetUserId = input('target_user_id');
        
        // 检查对方是否在线
        if (!check_user_online($targetUserId)) {
            return $this->fail('对方当前不在线，无法发起通话');
        }
        
        // 继续通话逻辑...
        return $this->success('通话发起成功');
    }
}
```

### 3. 在用户列表中使用

```php
public function getUserList()
{
    $users = User::limit(20)->select();
    
    // 批量检查在线状态
    $userIds = array_column($users->toArray(), 'user_id');
    $onlineUsers = batch_check_users_online($userIds);
    
    // 添加在线状态
    foreach ($users as &$user) {
        $user['is_online'] = in_array($user['user_id'], $onlineUsers);
    }
    
    return $this->success('查询成功', $users);
}
```

## ⚠️ 注意事项

### 1. 性能考虑
- 批量查询比单个查询更高效
- 建议使用缓存减少API调用频率
- 大量用户查询时建议分页处理

### 2. 错误处理
- 所有方法都包含异常处理
- 网络异常时会返回安全的默认值
- 详细错误信息记录在日志中

### 3. 缓存策略
- 测试接口使用5分钟缓存
- 生产环境可根据需要调整缓存时间
- 支持强制刷新缓存

### 4. 日志记录
- 所有API调用都有详细日志
- 可通过日志分析API调用情况
- 便于问题排查和性能优化

## 🚀 快速测试

1. **确保IM配置正确**：检查腾讯云IM相关配置
2. **创建测试用户**：确保数据库中有测试用户数据
3. **访问测试接口**：使用上述接口进行测试
4. **查看日志**：检查日志文件确认API调用情况

现在您可以使用这些接口来测试和验证IM在线状态查询功能了！
