<?php

namespace app\common\service\pay;

use app\common\enum\pay\PayEnum;
use app\common\service\ConfigService;

/**
 * 支付驱动类
 * Class PayDriver
 * @package app\common\service\pay
 */
class PayDriver
{
    /**
     * 错误信息
     * @var string|null
     */
    protected $error = null;

    /**
     * 默认支付引擎
     * @var string
     */
    protected $defaultEngine;

    /**
     * 支付引擎实例
     * @var object
     */
    protected $engine;

    /**
     * 支付方式
     * @var string
     */
    protected $payWay;


    /**
     * 获取引擎类名
     * @param string $payWay 支付方式
     * @return string
     */
    protected function getEngineClass($payWay)
    {
        $engineMap = [
            PayEnum::WECHAT_APP => 'WechatAppPay',
            PayEnum::ALIPAY_APP => 'AlipayAppPay',
            PayEnum::XIAOYE_PAY => 'XiaoyePay',
        ];

        $engineName = $engineMap[$payWay] ?? '';
        return __NAMESPACE__ . '\\engine\\' . $engineName;
    }

    /**
     * 获取错误信息
     * @return string|null
     */
    public function getError()
    {
        return $this->error;
    }


    /**
     * 获取引擎实例
     * @param string|null $payWay 支付方式
     * @param array|null $config 支付配置
     * @return object|null
     */
    public function getEngine($payWay = null, $config = null)
    {
        // 如果传入了参数，创建新的引擎实例
        if ($payWay && $config) {
            return $this->createEngineInstance($payWay, $config);
        }

        // 否则返回已初始化的引擎实例
        return $this->engine;
    }

    /**
     * 创建引擎实例
     * @param string $payWay 支付方式
     * @param array $config 支付配置
     * @return object|null
     */
    protected function createEngineInstance($payWay, $config)
    {
        try {
            // 验证支付方式是否有效
            if (!in_array($payWay, [PayEnum::WECHAT_APP, PayEnum::ALIPAY_APP, PayEnum::XIAOYE_PAY])) {
                throw new \Exception('不支持的支付方式: ' . $payWay);
            }

            // 构建引擎类名
            $engineClass = $this->getEngineClass($payWay);
            if (!class_exists($engineClass)) {
                throw new \Exception('支付引擎类不存在: ' . $engineClass);
            }

            // 实例化引擎
            $engine = new $engineClass($config);
            if ($engine->hasError()) {
                throw new \Exception($engine->getError());
            }

            return $engine;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return null;
        }
    }
}
