<?php
namespace app\common\service\tim;

use think\facade\Log;
use app\common\service\tim\SignApi;
use app\common\service\LogService;
use app\common\service\ConfigService;

class TencentImService
{
    private $sdkAppId;
    private $secretId;
    private $secretKey;
    private $apiUrl = 'https://console.tim.qq.com/v4/';

    public function __construct()
    {
        // 从配置中读取腾讯云IM参数
        $this->sdkAppId = ConfigService::get('tencent_im_config', 'tencent_sdkappid', '1600099725');
        $this->secretId = ConfigService::get('tencent_im_config', 'tencent_identifier', 'administrator');
        $this->secretKey = ConfigService::get('tencent_im_config', 'tencent_secret_key', '7c89959cacbef3aca6c67a574b556b19176873d75fa6f4a212a3dc3a73187417');

        // 验证必需配置
        if (empty($this->sdkAppId) || empty($this->secretId) || empty($this->secretKey)) {
            LogService::write('腾讯云IM配置不完整', [
                'sdkAppId' => $this->sdkAppId,
                'secretId' => $this->secretId,
                'secretKey_length' => strlen($this->secretKey)
            ], 'im_config_error');

            throw new \Exception('腾讯云IM配置不完整，请检查 tencent_sdkappid、tencent_identifier、tencent_secret_key 配置');
        }
    }


    /**
     * 发送API请求
     */
    private function sendRequest($service, $command, $userId, $data)
    {
        try {
            $random = rand(100000, 999999); // 添加随机数参数
            $url = sprintf("%s%s/%s?usersig=%s&identifier=%s&sdkappid=%s&random=%s&contenttype=json",
                $this->apiUrl,
                $service,
                $command,
                $this->generateAdminSig(),
                'administrator',
                $this->sdkAppId,
                $random
            );
            $options = [
                'http' => [
                    'method'  => 'POST',
                    'header'  => "Content-Type: application/json\r\n",
                    'content' => json_encode($data)
                ]
            ];
            
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);

            if ($result === false) {
                LogService::write('IM API请求失败', [
                    'url' => $url,
                    'user_id' => $userId,
                    'request_data' => $data,
                    'error' => 'file_get_contents返回false'
                ], 'im_api');
                return ['ErrorCode' => 500, 'ErrorInfo' => 'API请求失败'];
            }

            $response = json_decode($result, true);

            // 记录API调用日志（可选，用于调试）
            LogService::write('IM API请求', [
                'url' => $url,
                'user_id' => $userId,
                'request_data' => $data,
                'response' => $response
            ], 'im_api_debug');

            return $response;
        } catch (\Exception $e) {
                LogService::write('IM API请求失败', [
                    'url' => $url,
                    'user_id' => $userId,
                    'raw' => $e->getMessage()
                ],'im_api');
                
            return ['ErrorCode' => 500, 'ErrorInfo' => $e->getMessage()];
        }
    }

    /**
     * 创建IM用户
     */
    public function createAccount($userId, $nickname, $avatar = '')
    {
        $data = [
            'Identifier' => (string)$userId,
            'Nick' => $nickname,
            'FaceUrl' => $avatar
        ];

        $response = $this->sendRequest('im_open_login_svc', 'account_import', $this->secretId ,$data);

        return [
            'status' => $response['ErrorCode'] === 0,
            'data' => $response,
            'timestamp' => time()
        ];
    }

    //更新IM用户资料
    public function updateAccount($userId, $nickname, $avatar = '')
    {
        $data = [
            'From_Account' => (string)$userId,
            'ProfileItem' => [
                [
                    'Tag' => 'Tag_Profile_IM_Nick',
                    'Value' => $nickname
                ],
                [
                    'Tag' => 'Tag_Profile_IM_Image',
                    'Value' => $avatar
                ]
            ]
        ];

        $response = $this->sendRequest('profile', 'portrait_set', $this->secretId, $data);
        
        return [
            'status' => $response['ErrorCode'] === 0,
            'data' => $response
        ];
    }
    // 新增管理员签名生成方法
    private function generateAdminSig()
    {
        $api = new SignApi($this->sdkAppId, $this->secretKey);
        return $api->genSig($this->secretId); 
    }

    // 用户UserSig生成
    public function generateUserSig($userId)
    {
        $api = new SignApi($this->sdkAppId, $this->secretKey);
        $usersig = $api->genSig($userId);
        return $usersig;
    }

    // 获取基础配置
    public function getBaseConfig($userId)
    {
        return [
            'sdkappid' => $this->sdkAppId,
            'identifier' => (string)$userId, // 直接返回用户ID作为identifier
            'usersig' => $this->generateUserSig($userId)
        ];
    }

    //发送消息
    public function sendCustomMessage($fromUserId, $toUserId, $msgBody)
    {
        $data = array(
            'From_Account' => (string)$fromUserId, //发送方用户ID（字符串类型）
            'To_Account'   => (string)$toUserId,   //接收方用户ID（字符串类型）
            'MsgSeq'       => mt_rand(100000, 999999),
            'MsgRandom'    => mt_rand(100000, 999999),
            'MsgTimeStamp' => time(),
            'MsgBody'      => $msgBody,
        );
        $response = $this->sendRequest("openim", "sendmsg", $this->secretId, $data);
        return [
            'status' => $response['ErrorCode'] === 0,
            'data' => $response
        ];
    }

    /**
     * 发送群组消息
     * @param string $from_account 发送者账号
     * @param string $group_id 群组ID
     * @param array $msg_content 消息内容
     * @return array|false
     */
    public function sendGroupMessage($from_account, $group_id, $msg_content)
    {
        try {
            $data = [
                'GroupId' => $group_id,
                'From_Account' => $from_account,
                'Random' => rand(1, 65535),
                'MsgBody' => $msg_content
            ];

            $response = $this->sendRequest('group_open_http_svc', 'send_group_msg', $this->secretId, $data);

            if ($response && isset($response['ErrorCode'])) {
                if ($response['ErrorCode'] === 0) {
                    LogService::write('群组消息发送成功', [
                        'from_account' => $from_account,
                        'group_id' => $group_id,
                        'response' => $response
                    ], 'im_api_debug');
                } else {
                    LogService::write('群组消息发送失败', [
                        'from_account' => $from_account,
                        'group_id' => $group_id,
                        'response' => $response
                    ], 'im_api');
                }
                return $response;
            }

            return false;
        } catch (\Exception $e) {
            LogService::write('群组消息发送异常', [
                'from_account' => $from_account,
                'group_id' => $group_id,
                'error' => $e->getMessage()
            ], 'im_api');
            return false;
        }
    }

    /**
     * 创建群组
     * @param string $groupType 群组类型 (Private/Public/ChatRoom/AVChatRoom/BChatRoom)
     * @param string $groupId 群组ID (可选，不传则系统自动生成)
     * @param string $ownerAccount 群主账号
     * @param string $groupName 群组名称
     * @param string $introduction 群组简介
     * @param array $memberList 初始成员列表
     * @return array
     */
    public function createGroup($groupType, $groupId = '', $ownerAccount = '', $groupName = '', $introduction = '', $memberList = [])
    {
        try {
            $data = [
                'Type' => $groupType,
                'Name' => $groupName ?: 'Group',
                'Introduction' => $introduction
            ];

            // 如果指定了群组ID
            if (!empty($groupId)) {
                $data['GroupId'] = $groupId;
            }

            // 如果指定了群主
            if (!empty($ownerAccount)) {
                $data['Owner_Account'] = $ownerAccount;
            }

            // 如果有初始成员
            if (!empty($memberList)) {
                $data['MemberList'] = $memberList;
            }

            $response = $this->sendRequest('group_open_http_svc', 'create_group', $this->secretId, $data);

            LogService::write('创建群组', [
                'group_type' => $groupType,
                'group_id' => $groupId,
                'owner_account' => $ownerAccount,
                'group_name' => $groupName,
                'response' => $response
            ], 'im_api');

            return $response;
        } catch (\Exception $e) {
            LogService::write('创建群组异常', [
                'group_type' => $groupType,
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ], 'im_api');
            return ['ErrorCode' => 500, 'ErrorInfo' => $e->getMessage()];
        }
    }

    /**
     * 创建全员广播大群 (BChatRoom类型)
     * @param string $groupId 群组ID
     * @param string $ownerAccount 群主账号
     * @param string $groupName 群组名称
     * @param string $introduction 群组简介
     * @return array
     */
    public function createBroadcastGroup($groupId, $ownerAccount = '', $groupName = 'FullGroup', $introduction = '')
    {
        return $this->createGroup('BChatRoom', $groupId, $ownerAccount, $groupName, $introduction);
    }

    /**
     * 创建音视频聊天室 (AVChatRoom类型)
     * @param string $groupId 群组ID (可选)
     * @param string $ownerAccount 群主账号
     * @param string $groupName 群组名称
     * @param string $introduction 群组简介
     * @return array
     */
    public function createAVChatRoom($groupId = '', $ownerAccount = '', $groupName = 'AVChatRoom', $introduction = '')
    {
        return $this->createGroup('AVChatRoom', $groupId, $ownerAccount, $groupName, $introduction);
    }

    /**
     * 销毁群组
     * @param string $groupId 群组ID
     * @return array
     */
    public function destroyGroup($groupId)
    {
        try {
            $data = [
                'GroupId' => $groupId
            ];

            $response = $this->sendRequest('group_open_http_svc', 'destroy_group', $this->secretId, $data);

            LogService::write('销毁群组', [
                'group_id' => $groupId,
                'response' => $response
            ], 'im_api');

            return $response;
        } catch (\Exception $e) {
            LogService::write('销毁群组异常', [
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ], 'im_api');
            return ['ErrorCode' => 500, 'ErrorInfo' => $e->getMessage()];
        }
    }

    /**
     * 检查单个用户是否在线
     * @param string $userId 用户ID
     * @return bool 是否在线
     */
    public function isUserOnline($userId)
    {
        try {
            // 转换为字符串格式
            $userId = strval($userId);

            $data = [
                'To_Account' => [$userId]
            ];

            $response = $this->sendRequest('openim', 'querystate', $this->secretId, $data);

            LogService::write('查询用户在线状态', [
                'user_id' => $userId,
                'response' => $response
            ], 'im_api_debug');

            if (isset($response['QueryResult']) && !empty($response['QueryResult'])) {
                $userStatus = $response['QueryResult'][0];
                return isset($userStatus['State']) && $userStatus['State'] === 'Online';
            }

            return false;
        } catch (\Exception $e) {
            LogService::write('检查用户在线状态异常', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ], 'im_api');
            return false;
        }
    }


}
