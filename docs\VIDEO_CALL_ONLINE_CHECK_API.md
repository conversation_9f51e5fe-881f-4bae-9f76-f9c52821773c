# 视频通话用户在线状态查询API

## 🎯 功能说明

在VideoCallController中添加了用户在线状态查询API，用于在发起视频通话前检查对方是否在线。

## 📋 API接口

### 查询用户是否在线

**接口地址**: `/applent/video_call/checkOnline`
**请求方式**: POST
**是否需要登录**: 是

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 要查询的用户ID |

#### 请求示例
```json
POST /applent/video_call/checkOnline
{
    "user_id": 1001
}
```

#### 响应示例

**成功响应**:
```json
{
    "code": 1,
    "msg": "操作成功",
    "data": {
        "user_id": 1001,
        "is_online": true,
        "online_status": "在线",
        "check_time": "2024-01-15 14:30:25"
    }
}
```

**用户离线响应**:
```json
{
    "code": 1,
    "msg": "操作成功", 
    "data": {
        "user_id": 1002,
        "is_online": false,
        "online_status": "离线",
        "check_time": "2024-01-15 14:30:25"
    }
}
```

**错误响应**:
```json
{
    "code": 0,
    "msg": "查询用户在线状态失败: 网络异常"
}
```

## 🚀 使用场景

### 1. 发起视频通话前检查
```javascript
// 前端调用示例
async function startVideoCall(targetUserId) {
    try {
        // 先检查对方是否在线
        const checkResult = await api.post('/applent/video_call/checkOnline', {
            user_id: targetUserId
        });
        
        if (!checkResult.data.is_online) {
            showMessage('对方当前不在线，无法发起通话');
            return;
        }
        
        // 对方在线，继续发起通话
        const callResult = await api.post('/applent/video_call/start', {
            to_user_id: targetUserId,
            call_type: 2 // 视频通话
        });
        
        // 处理通话结果...
        
    } catch (error) {
        showMessage('操作失败: ' + error.message);
    }
}
```

### 2. 用户列表显示在线状态
```javascript
// 批量检查多个用户的在线状态
async function getUserListWithOnlineStatus(userIds) {
    const users = [];
    
    for (const userId of userIds) {
        try {
            const result = await api.post('/applent/video_call/checkOnline', {
                user_id: userId
            });
            
            users.push({
                user_id: userId,
                is_online: result.data.is_online,
                online_status: result.data.online_status
            });
        } catch (error) {
            console.error(`查询用户${userId}在线状态失败:`, error);
            users.push({
                user_id: userId,
                is_online: false,
                online_status: '未知'
            });
        }
    }
    
    return users;
}
```

## 🔧 后端实现

### 1. 控制器方法
```php
/**
 * 查询用户是否在线
 * @return \think\response\Json
 */
public function checkOnline()
{
    $params = (new VideoCallValidate())->post()->goCheck('checkOnline');
    
    try {
        $userId = $params['user_id'];
        
        // 查询用户是否在线
        $isOnline = check_user_online($userId);
        
        return $this->data([
            'user_id' => $userId,
            'is_online' => $isOnline,
            'online_status' => $isOnline ? '在线' : '离线',
            'check_time' => date('Y-m-d H:i:s')
        ]);
        
    } catch (\Exception $e) {
        return $this->fail('查询用户在线状态失败: ' . $e->getMessage());
    }
}
```

### 2. 验证器规则
```php
protected $rule = [
    'user_id' => 'require|number'
];

protected $message = [
    'user_id.require' => '用户ID不能为空',
    'user_id.number'  => '用户ID必须为数字'
];

public function sceneCheckOnline()
{
    return $this->only(['user_id']);
}
```

### 3. 核心查询函数
```php
/**
 * 检查用户是否在线
 * @param int $user_id 用户ID
 * @return bool 是否在线
 */
function check_user_online($user_id)
{
    try {
        $imService = get_im_service_instance();
        return $imService->isUserOnline($user_id);
    } catch (\Exception $e) {
        LogService::write('检查用户在线状态失败', [
            'user_id' => $user_id,
            'error' => $e->getMessage()
        ], 'im_common');
        return false;
    }
}
```

## ⚠️ 注意事项

### 1. 性能考虑
- 每次查询都会调用腾讯云IM接口
- 建议在关键场景使用，避免频繁调用
- 可以考虑添加短时间缓存优化性能

### 2. 错误处理
- 网络异常时返回false（离线状态）
- 所有异常都会记录详细日志
- 前端需要处理接口调用失败的情况

### 3. 用户状态说明
- **true**: 用户在线，可以发起通话
- **false**: 用户离线或查询失败，不建议发起通话

### 4. 日志记录
- 所有查询操作都会记录日志
- 日志分类：`im_common`（正常日志）、`im_api`（错误日志）
- 便于问题排查和性能分析

## 🎯 最佳实践

### 1. 发起通话前必须检查
```php
// 在VideoCallLogic::startCall方法中添加检查
public static function startCall($params)
{
    $targetUserId = $params['to_user_id'];
    
    // 检查对方是否在线
    if (!check_user_online($targetUserId)) {
        throw new \Exception('对方当前不在线，无法发起通话');
    }
    
    // 继续通话逻辑...
}
```

### 2. 添加缓存优化（可选）
```php
function check_user_online_with_cache($user_id, $cache_time = 60)
{
    $cacheKey = "user_online_check_{$user_id}";
    $cached = cache($cacheKey);
    
    if ($cached !== null) {
        return $cached;
    }
    
    $isOnline = check_user_online($user_id);
    cache($cacheKey, $isOnline, $cache_time);
    
    return $isOnline;
}
```

### 3. 批量查询优化
如果需要批量查询，建议分批处理避免超时：
```php
function batch_check_users_online($userIds, $batchSize = 10)
{
    $results = [];
    $batches = array_chunk($userIds, $batchSize);
    
    foreach ($batches as $batch) {
        foreach ($batch as $userId) {
            $results[$userId] = check_user_online($userId);
            usleep(100000); // 休息0.1秒避免频繁调用
        }
    }
    
    return $results;
}
```

现在您可以使用 `/applent/video_call/checkOnline` 接口来查询单个用户的在线状态了！
