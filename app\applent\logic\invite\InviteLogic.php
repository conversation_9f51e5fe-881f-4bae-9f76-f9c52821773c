<?php

namespace app\applent\logic\invite;

use app\common\logic\BaseLogic;
use app\common\service\ConfigService;
use app\common\service\FileService;
use app\common\model\user\UserInviteRecord;
use app\common\model\user\UserIncomeLog;
use app\common\model\user\User;
use think\facade\Cache;
use think\facade\Log;

/**
 * 邀请逻辑
 * Class InviteLogic
 * @package app\applent\logic\invite
 */
class InviteLogic extends BaseLogic
{
    /**
     * 生成邀请海报（支持多张海报）
     * @param int $userId 用户ID
     * @return array|false
     */
    public static function generatePoster(int $userId)
    {
        try {
            //删除缓存
            // Cache::delete('invite_poster_' . $userId);
            // 检查缓存中是否已有该用户的海报
            $cacheKey = 'invite_poster_' . $userId;
            $cachedPosters = Cache::get($cacheKey);

            if ($cachedPosters) {
                return ['poster_urls' => $cachedPosters, 'from_cache' => true];
            }

            // 获取配置信息
            $posterImgs = ConfigService::get('systemconfig', 'poster_img', '');
            $shareUrl = ConfigService::get('systemconfig', 'share_url', '');
 
            // 处理海报图片配置，获取所有有效的海报图片
            //$validPosterImgs = self::getValidPosterImages($posterImgs);
 
            if (empty($posterImgs)) {
                throw new \Exception('海报图片配置不存在');
            }

            if (empty($shareUrl)) {
                throw new \Exception('分享链接配置不存在');
            }

            // 生成带用户ID的分享链接
            $userShareUrl = self::buildUserShareUrl($shareUrl, $userId);

            // 生成二维码（所有海报共用同一个二维码）
            $qrCodePath = self::generateQRCode($userShareUrl, $userId);

            if (!$qrCodePath) {
                throw new \Exception('二维码生成失败');
            }

            // 批量生成所有海报
            $posterUrls = [];
            $tempFiles = [$qrCodePath]; // 用于清理的临时文件列表
  
            foreach ($posterImgs as $index => $posterImg) {

                try {
                    // 合成海报
                    $posterImgUrl = FileService::getFileUrl($posterImg);
                    $posterPath = self::composePoster($posterImgUrl, $qrCodePath, $userId, $index);

                    if (!$posterPath) {
                        Log::warning("海报合成失败，跳过: {$posterImg}");
                        continue;
                    }

                    $tempFiles[] = $posterPath;
                       
                    // 上传到云存储
                    $uploadResult = self::uploadPoster($posterPath, $userId, $index);
                    if ($uploadResult) {
                        $posterUrls[] = $uploadResult;
                    } else {
                        Log::warning("海报上传失败，跳过: {$posterImg}");
                    }

                } catch (\Exception $e) {
                    Log::warning("处理海报失败，跳过: {$posterImg}, 错误: " . $e->getMessage());
                    continue;
                }
            }

            // 清理临时文件
            self::cleanupTempFiles($tempFiles);

            if (empty($posterUrls)) {
                throw new \Exception('所有海报生成失败');
            }

            // 缓存结果
            Cache::set($cacheKey, $posterUrls, 0); // 0表示永不过期

            return ['poster_urls' => $posterUrls, 'from_cache' => false];

        } catch (\Exception $e) {
            Log::error('生成邀请海报失败: ' . $e->getMessage());
            throw new \Exception('生成邀请海报失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取所有有效的海报图片（支持单张和多张）
     * @param mixed $posterImgs 海报图片配置
     * @return array 有效的海报图片路径数组
     */
    private static function getValidPosterImages($posterImgs): array
    {
        if (empty($posterImgs)) {
            return [];
        }

        // 如果是字符串，转换为数组（兼容原有单张海报配置）
        if (is_string($posterImgs)) {
            $trimmed = trim($posterImgs);
            return empty($trimmed) ? [] : [$trimmed];
        }

        // 如果是数组，过滤掉空值
        if (is_array($posterImgs)) {
            $validPosters = array_filter($posterImgs, function($img) {
                return !empty(trim($img));
            });

            // 重新索引数组
            return array_values($validPosters);
        }

        return [];
    }

    /**
     * 构建用户分享链接
     * @param string $baseUrl 基础链接
     * @param int $userId 用户ID
     * @return string
     */
    private static function buildUserShareUrl(string $baseUrl, int $userId): string
    {
        $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
        return $baseUrl . $separator . 'inviter_code=' . $userId;
    }

    /**
     * 生成二维码（使用endroid/qr-code库）
     * @param string $content 二维码内容
     * @param int $userId 用户ID
     * @return string|false 返回二维码文件路径或false
     */
    private static function generateQRCode(string $content, int $userId)
    {
        try {
            // 创建临时文件目录
            $tempDir = runtime_path('temp');
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $qrCodePath = $tempDir . '/qr_' . $userId . '_' . time() . '.png';

            // 使用endroid/qr-code v4+ 的新API
            if (class_exists('\Endroid\QrCode\Builder\Builder')) {
                // v4+ 版本使用Builder模式
                $result = \Endroid\QrCode\Builder\Builder::create()
                    ->writer(new \Endroid\QrCode\Writer\PngWriter())
                    ->data($content)
                    ->encoding(new \Endroid\QrCode\Encoding\Encoding('UTF-8'))
                    ->errorCorrectionLevel(new \Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelLow())
                    ->size(200)
                    ->margin(10)
                    ->roundBlockSizeMode(new \Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeMargin())
                    ->validateResult(false)
                    ->build();

                file_put_contents($qrCodePath, $result->getString());

            } elseif (class_exists('\Endroid\QrCode\QrCode')) {
                // v3.x 版本使用传统方式
                $qrCode = new \Endroid\QrCode\QrCode($content);
                $qrCode->setSize(200);
                $qrCode->setMargin(10);

                // v3.x 使用Writer
                $writer = new \Endroid\QrCode\Writer\PngWriter();
                $result = $writer->write($qrCode);

                file_put_contents($qrCodePath, $result->getString());

            } else {
                throw new \Exception('endroid/qr-code库未正确安装');
            }

            return $qrCodePath;

        } catch (\Exception $e) {
            Log::error('本地二维码生成失败: '.$e->getMessage());
            throw new \Exception('二维码生成失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 合成海报
     * @param string $posterImgUrl 海报背景图URL
     * @param string $qrCodePath 二维码文件路径
     * @param int $userId 用户ID
     * @param int $index 海报索引（用于区分多张海报）
     * @return string|false 返回合成后的海报路径或false
     */
    private static function composePoster(string $posterImgUrl, string $qrCodePath, int $userId, int $index = 0)
    {
        try {
           
            // 下载海报背景图（增加重试机制）
            $posterData = file_get_contents($posterImgUrl, false, stream_context_create([
                'http' => ['timeout' => 15] // 增加超时时间
            ]));
            if ($posterData === false) {
                Log::error('海报背景图下载失败: ' . $posterImgUrl);
                return false;
            }

            // 保存临时文件（增加格式自动识别）
            $tempDir = runtime_path('temp');
            $posterBgPath = $tempDir . '/poster_bg_' . $userId . '_' . $index . '_' . time() . '.tmp';
            file_put_contents($posterBgPath, $posterData);

            // 获取海报尺寸（增加错误处理）
            $posterInfo = @getimagesize($posterBgPath);
            if (!$posterInfo) {
                Log::error('无效的海报图片格式: ' . $posterImgUrl);
                @unlink($posterBgPath);
                return false;
            }

            // 将二维码放置在海报下方正中间，距离底部100像素
            $qrSize = (int) (min($posterInfo[0], $posterInfo[1]) * 0.25); // 二维码占背景25%宽度

            // 计算位置（转换为整数避免精度丢失警告）
            $qrX = (int) (($posterInfo[0] - $qrSize) / 2); // 水平居中
            $qrY = (int) ($posterInfo[1] - $qrSize - 60); // 距离底部100像素

            // 合并图片（增加透明度处理）
            $finalPosterPath = self::mergeImages($posterBgPath, $qrCodePath, $qrX, $qrY, $qrSize, $userId, $index);
            
            // 清理临时文件
            @unlink($posterBgPath);
            
            return $finalPosterPath;

        } catch (\Exception $e) {
            Log::error('海报合成失败: ' . $e->getMessage());
            throw new \Exception('海报合成失败: ' . $e->getMessage());
        }
    }

    /**
     * 使用GD库合并图片
     * @param string $bgPath 背景图路径
     * @param string $qrPath 二维码路径
     * @param int $x X坐标
     * @param int $y Y坐标
     * @param int $qrSize 二维码大小
     * @param int $userId 用户ID
     * @param int $index 海报索引（用于区分多张海报）
     * @return string|false 返回合成后的图片路径或false
     */
     private static function mergeImages(string $bgPath, string $qrPath, int $x, int $y, int $qrSize, int $userId, int $index = 0)
    {
        try {
            // 创建背景图资源（支持更多格式）
            $bgImage = self::createImageResource($bgPath);
            if (!$bgImage) return false;

            // 创建二维码资源（带透明度处理）
            $qrImage = imagecreatefrompng($qrPath);
            imagealphablending($qrImage, true);
            imagesavealpha($qrImage, true);

            // 调整二维码尺寸（保持清晰度）
            $qrResized = imagescale($qrImage, $qrSize, $qrSize, IMG_BICUBIC);
            
            // 合并到背景（带阴影效果）
            self::drawShadow($bgImage, $x-2, $y-2, $qrSize+4, $qrSize+4); // 添加阴影
            
            imagecopyresampled(
                $bgImage, $qrResized,
                $x, $y,
                0, 0,
                $qrSize, $qrSize,
                imagesx($qrResized), imagesy($qrResized)
            );

            // 保存合成图（优化压缩质量）
            $finalPath = runtime_path('temp') . '/poster_final_' . $userId . '_' . $index . '_' . time() . '.jpg';
            imagejpeg($bgImage, $finalPath, 85); // 85%质量平衡清晰度和体积

            // 释放资源
            imagedestroy($bgImage);
            imagedestroy($qrImage);
            imagedestroy($qrResized);

            return $finalPath;

        } catch (\Exception $e) {
            Log::error('图片合并失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 创建图片资源（修复版本 - 不依赖EXIF扩展）
     */
    private static function createImageResource(string $path)
    {
        // 检查文件是否存在
        if (!file_exists($path)) {
            Log::error('图片文件不存在: ' . $path);
            return false;
        }

        // 使用getimagesize()替代exif_imagetype()
        $imageInfo = getimagesize($path);
        if ($imageInfo === false) {
            Log::error('无法获取图片信息: ' . $path);
            return false;
        }

        $type = $imageInfo[2]; // 图片类型

        try {
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $image = imagecreatefromjpeg($path);
                    break;
                case IMAGETYPE_PNG:
                    $image = imagecreatefrompng($path);
                    // 保持PNG透明度
                    imagealphablending($image, false);
                    imagesavealpha($image, true);
                    break;
                case IMAGETYPE_GIF:
                    $image = imagecreatefromgif($path);
                    break;
                case IMAGETYPE_WEBP:
                    if (function_exists('imagecreatefromwebp')) {
                        $image = imagecreatefromwebp($path);
                    } else {
                        Log::error('WebP格式不支持: ' . $path);
                        return false;
                    }
                    break;
                default:
                    Log::error('不支持的图片格式: ' . $type . ', 文件: ' . $path);
                    return false;
            }

            if ($image === false) {
                Log::error('创建图片资源失败: ' . $path);
                return false;
            }

            return $image;

        } catch (\Exception $e) {
            Log::error('创建图片资源异常: ' . $e->getMessage() . ', 文件: ' . $path);
            return false;
        }
    }

    /**
     * 绘制阴影效果（优化版本）
     */
    private static function drawShadow($image, $x, $y, $w, $h)
    {
        try {
            // 检查参数有效性
            if ($w <= 0 || $h <= 0) {
                return;
            }

            // 创建阴影画布
            $shadow = imagecreatetruecolor($w, $h);
            if ($shadow === false) {
                Log::warning('创建阴影画布失败');
                return;
            }

            // 启用alpha混合
            imagealphablending($shadow, false);
            imagesavealpha($shadow, true);

            // 创建半透明黑色
            $shadowColor = imagecolorallocatealpha($shadow, 0, 0, 0, 80);
            if ($shadowColor === false) {
                imagedestroy($shadow);
                return;
            }

            // 填充阴影颜色
            imagefill($shadow, 0, 0, $shadowColor);

            // 应用高斯模糊（如果支持）
            if (function_exists('imagefilter')) {
                for ($i = 0; $i < 3; $i++) {
                    imagefilter($shadow, IMG_FILTER_GAUSSIAN_BLUR);
                }
            }

            // 合并阴影到主图像
            imagecopymerge(
                $image, $shadow,
                $x, $y,
                0, 0,
                $w, $h,
                40 // 40%透明度
            );

            // 清理资源
            imagedestroy($shadow);

        } catch (\Exception $e) {
            Log::warning('绘制阴影失败: ' . $e->getMessage());
        }
    }
    /**
     * 上传海报到云存储（参考UploadService::image方法）
     * @param string $posterPath 海报文件路径
     * @param int $userId 用户ID
     * @param int $index 海报索引（用于区分多张海报）
     * @return string|false 返回上传后的URL或false
     */
    private static function uploadPoster(string $posterPath, int $userId, int $index = 0)
    {
        try {
            
            // 检查文件是否存在
            if (!file_exists($posterPath)) {
                Log::error('海报文件不存在: ' . $posterPath);
                throw new \Exception('海报文件不存在');
            }

            // 获取文件大小
            $fileSize = filesize($posterPath);

            // 配置存储驱动
            $config = [
                'default' => ConfigService::get('storage', 'default', 'local'),
                'engine'  => ConfigService::get('storage') ?? ['local'=>[]],
            ];
            // 创建存储驱动实例
            $storageDriver = new \app\common\service\storage\Driver($config);

            // 设置要上传的文件（使用真实文件路径）
            $storageDriver->setUploadFileByReal($posterPath);

            // 获取文件名和信息
            $fileName = $storageDriver->getFileName();
            $uploadFileInfo = $storageDriver->getFileInfo();
            // 检查文件信息是否正确获取
            if (empty($uploadFileInfo) || empty($fileName)) {
                Log::error('获取文件信息失败');
                throw new \Exception('获取文件信息失败');
            }
            // 验证文件扩展名
            $allowedExts = ['jpg', 'jpeg', 'png', 'gif'];

            // 从文件名获取扩展名（因为setUploadFileByReal可能不设置ext字段）
            $fileExt = strtolower(pathinfo($uploadFileInfo['name'], PATHINFO_EXTENSION));

            if (!in_array($fileExt, $allowedExts)) {
                Log::error('不支持的文件格式: ' . $fileExt);
                throw new \Exception('不支持的文件格式: ' . $fileExt);
            }

            // 验证文件大小（最大10MB）
            $maxSize = 10 * 1024 * 1024; // 10MB
            if ($fileSize > $maxSize) {
                Log::error('文件大小超过限制: ' . $fileSize . ' bytes');
                throw new \Exception('文件大小超过限制，最大支持10MB');
            }

            // 设置保存目录
            $saveDir = 'uploads/invite_posters';

            // 执行上传
            if (!$storageDriver->upload($saveDir)) {
                $error = $storageDriver->getError();
                Log::error('文件上传失败: ' . $error);
                throw new \Exception('文件上传失败: ' . $error);
            }

            // 构建文件URI
            $fileUri = $saveDir . '/' . str_replace("\\", "/", $fileName);

            // 写入数据库记录（可选，用于文件管理）
            try {
                \app\common\model\file\File::create([
                    'cid'         => 0, // 邀请海报分类
                    'type'        => \app\common\enum\FileEnum::IMAGE_TYPE,
                    'name'        => 'invite_poster_' . $userId . '_' . $index . '_' . time() . '.' . $fileExt,
                    'uri'         => $fileUri,
                    'source'      => \app\common\enum\FileEnum::SOURCE_USER,
                    'source_id'   => $userId,
                    'create_time' => time(),
                ]);
            } catch (\Exception $e) {
                Log::warning('写入文件记录失败: ' . $e->getMessage());
                // 不影响主流程，继续执行
            }

            // 获取完整的文件URL
            $fileUrl = \app\common\service\FileService::getFileUrl($fileUri);

            Log::info('海报上传成功: ' . $fileUrl);
            return $fileUrl;

        } catch (\Exception $e) {
            Log::error('海报上传失败: ' . $e->getMessage());
            throw new \Exception('海报上传失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理临时文件
     * @param array $filePaths 文件路径数组
     */
    private static function cleanupTempFiles(array $filePaths): void
    {
        foreach ($filePaths as $filePath) {
            if ($filePath && file_exists($filePath)) {
                @unlink($filePath);
            }
        }
    }

    /**
     * @notes 获取邀请排行榜
     * @param string $type 时间类型：day, week, month
     * @param int $limit 获取数量，默认20
     * @return array
     */
    public static function getInviteRanking(array $params)
    {
        try {
            // 计算时间范围
            $timeRange = getTimeRange($params['type']);

            // 使用模型关联查询邀请排行榜数据
            $rankingData = UserInviteRecord::with(['inviter' => function($query) {
                    $query->field('id,nickname,sex,avatar')->where('delete_time', 'null');
                }])
                ->where('create_time', '>=', $timeRange['start'])
                ->where('create_time', '<=', $timeRange['end'])
                ->field('user_id, COUNT(invite_user_id) as invite_count')
                ->group('user_id')
                ->order('invite_count', 'desc')
                ->limit(20)
                ->select()
                ->toArray();

            // 处理数据格式
            $result = [];
            foreach ($rankingData as $index => $item) {
                // 检查关联的用户数据是否存在
                if (empty($item['inviter'])) {
                    continue; // 跳过已删除的用户
                }

                $result[] = [
                    'user_id' => $item['user_id'],
                    'nickname' => $item['inviter']['nickname'] ,
                    'sex' => $item['inviter']['sex'],
                    'avatar' => $item['inviter']['avatar'],
                    'invite_count' => (int)$item['invite_count']
                ];
            }

            return $result;

        } catch (\Exception $e) {
            static::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取收入排行榜
     * @param array $params 参数
     * @return array|false
     */
    public static function getIncomeRanking(array $params)
    {
        try {
            // 计算时间范围
            $timeRange = getTimeRange($params['type']);

            // 查询指定来源的收入数据，按用户分组统计总收入
            $rankingData = UserIncomeLog::with(['user' => function($query) {
                    $query->field('id,nickname,sex,avatar')->where('delete_time', 'null');
                }])
                ->where('create_time', '>=', $timeRange['start'])
                ->where('create_time', '<=', $timeRange['end'])
                ->whereIn('source', [3, 4, 5, 6, 7, 8, 11, 12, 13]) 
                ->field('user_id, SUM(change_income) as total_income')
                ->group('user_id')
                ->having('total_income > 0') // 只显示有收入的用户
                ->order('total_income', 'desc')
                ->limit(20)
                ->select()
                ->toArray();

            // 处理数据格式
            $result = [];
            foreach ($rankingData as $index => $item) {
                // 检查关联的用户数据是否存在
                if (empty($item['user'])) {
                    continue; // 跳过已删除的用户
                }

                $user = $item['user'];
                $result[] = [
                    'user_id' => $item['user_id'],
                    'nickname' => $user['nickname'],
                    'sex' => $user['sex'],
                    'avatar' => $user['avatar'],
                    'total_income' => round((float)$item['total_income'], 2)
                ];
            }

            return $result;

        } catch (\Exception $e) {
            static::setError('获取收入排行榜失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 邀请好友
     * @param int $userId 用户ID
     * @return array|false
     */
    public static function inviteFriend(int $userId)
    {
        try {
            //累计邀请人数
            $inviteCount = UserInviteRecord::where('user_id', $userId)->count();

            //累计收益金额
            $incomeTotal = UserIncomeLog::where('user_id', $userId)
                ->whereIn('source', [3, 4, 5, 6, 7, 8, 11, 12, 13])
                ->sum('change_income');
            $incomeTotal = round((float)$incomeTotal, 2);

            //分享链接
            $shareUrl = ConfigService::get('systemconfig', 'share_url', '').'?inviter_code='.$userId;

            //分享海报
            $posterImg = ConfigService::get('systemconfig', 'poster_img', []);
            foreach ($posterImg as $key => $value) {
                $posterImg[$key] = FileService::getFileUrl($value);
            }
            //首冲奖励
            $firstRechargeReward = ConfigService::get('systemconfig', 'first_charge_reward', 0);

            //充值奖励百分比
            $rechargeReward = ConfigService::get('systemconfig', 'one_recharge_rebate_ratio', 0);

            //收入百分比
            $audioRatio = ConfigService::get('systemconfig', 'one_audio_rebate_ratio', 0);
            $incomeRatio = ConfigService::get('systemconfig', 'one_gift_rebate_ratio', 0);
            $incomeRatio = $audioRatio > $incomeRatio ? $audioRatio : $incomeRatio;

            //充值VIP奖励
            $vipReward = ConfigService::get('systemconfig', 'one_vip_rebate_ratio', 0);

            return [
                'invite_count' => $inviteCount,
                'income_total' => $incomeTotal,
                'share_url' => $shareUrl,
                'poster_img' => $posterImg,
                'first_recharge_reward' => $firstRechargeReward,
                'recharge_reward' => $rechargeReward,
                'income_ratio' => $incomeRatio,
                'vip_reward' => $vipReward,
            ];

        } catch (\Exception $e) {
            static::setError($e->getMessage());
            return false;
        }
    }
}
